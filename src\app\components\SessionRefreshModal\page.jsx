"use client";
import React from "react";
import styles from "./pop.module.css";

const SessionModal = ({ onContinue, onCancel }) => {
  return (
    <div className={styles.cont}>
      <div className={styles.heading}>Your session will expire in 1 minute</div>
      <div className={styles.boxBody}>
        <p>Would you like to continue your session?</p>
        <div className={styles.btnContainer}>
          <button className={styles.btnGreen} onClick={onContinue}>
            Continue Session
          </button>
          <button className={styles.btnRed} onClick={onCancel}>
            End Session
          </button>
        </div>
      </div>
    </div>
  );
};

export default SessionModal;
