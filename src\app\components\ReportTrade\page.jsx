"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import styles from "./reportTrade.module.css";
import tick from "../../../../public/assets/tick.png";
import { helpDeskPostApi } from "@/app/api/helpDeskApis/helpDesk";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  addDisputeListApi,
  getDisputesOrderDropDownApi,
} from "@/app/api/disputeApi's/dispute";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const Page = ({ title, dispute, selectedId, orderNumber }) => {
  const [hide, setHide] = useState(false);
  const [hideReason, setHideReason] = useState(false);
  const [uploadMark, setUploadMark] = useState("");
  const [comment, setComment] = useState("");
  const [uploadEvidence, setUploadEvidence] = useState("");
  const [queryTitle, setQueryTitle] = useState("");
  const [loadDisputeDrop, setLoadDisputeDrop] = useState([]);
  const [selectedOrderNumber, setSelectedOrderNumber] = useState("");

  const handleReasonContainer = () => {
    setHideReason(!hideReason);
  };

  const handleUploadMark = (event) => {
    const file = event.target.files[0];
    setUploadEvidence(file);
    setUploadMark(file);
  };

  const validateFields = () => {
    if (!selectedOrderNumber || !uploadEvidence || !comment) {
      toast.error("All fields are required", { toastId: "error-toast" });
      return false;
    }
    return true;
  };

  const addHelpQuery = async () => {
    if (!validateFields()) {
      return;
    }

    const formData = new FormData();
    formData.append("disput_title", queryTitle);
    formData.append("upload_evidence", uploadEvidence);
    formData.append("comments", comment);
    formData.append("order_number", orderNumber);
    // formData.append("query_title", queryTitle);

    try {
      const response = await customFetchWithToken.post(
        "/dispute-list/",
        formData
      );

      toast.success(response.data.message, { toastId: "error-toast2" });
    } catch (err) {
      console.error(err);
      toast.error("An error occurred", { toastId: "error-toast3" });
    }
  };

  const loadDisputeDropdown = async () => {
    try {
      const res = await customFetchWithToken.get("/order-dispute-list/");
      setLoadDisputeDrop(res.data.data);
    } catch (error) {
      console.error(error);
    }
  };

  const handleOrderDropdown = (e) => {
    setQueryTitle(e.target.value);
  };

  useEffect(() => {
    loadDisputeDropdown();
  }, []);
  return (
    <div className={styles.disputeAction}>
      <div className={styles.disputeActionHearderArea}>
        <div className={styles.disputeActionHearder}>{title}</div>
      </div>

      <div className={styles.disputeActionBox}>
        <div className={styles.disputeActionBoxWrapper}>
          <div className={styles.orderDetails}>
            <label htmlFor="order">Order number</label>
            <input maxLength={100} value={orderNumber} />
          </div>
          <div className={styles.orderDetails}>
            <label htmlFor="order">Select Reason</label>
            <select
              className={styles.payMthodSelect}
              name="currencyPayout"
              id="order"
              value={selectedOrderNumber}
              onChange={handleOrderDropdown}
            >
              <option value="-1">Please select reason of dispute</option>
              <option value="1">Funds not received on time</option>
              <option value="2">Third Party payment</option>
              <option value="3">Adjust amount (underpaid)</option>
              <option value="4">Suspicious behaviour</option>
              <option value="5">Other</option>
            </select>
          </div>
          <div className={styles.uploadPayEvidence}>
            <div className={styles.uploadDetails}>
              <label htmlFor="upload">Upload payment evidence</label>
              <div className={styles.uploadDetailsCage}>
                <input
                  id="upload"
                  type="file"
                  placeholder="Enter trade number"
                  onChange={handleUploadMark}
                />
                <div className={styles.uploadBtn}>
                  {uploadMark ? (
                    <Image
                      className={styles.upload}
                      src={tick}
                      width={15}
                      height={15}
                      alt="backPNG"
                    />
                  ) : (
                    "upload"
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className={styles.commentArea}>
            <textarea
              className={styles.commentTextArea}
              id="comments"
              rows="8"
              placeholder="max length 200 Characters..."
              maxLength={200}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            ></textarea>
          </div>
          <div
            style={{
              background: "black",
              color: "whitesmoke",
              display: "flex",
              justifyContent: "center",
              height: "40px",
              alignItems: "center",
              borderRadius: "5px",
            }}
            className={styles.messageSubmitBtn}
            onClick={addHelpQuery}
          >
            Submit
          </div>
        </div>
      </div>
      {/* <ToastContainer limit={1} /> */}
    </div>
  );
};

export default Page;
