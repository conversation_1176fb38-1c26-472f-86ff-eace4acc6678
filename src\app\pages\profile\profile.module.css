.mainContainer {
    display: flex;
    flex-direction: column;

}

.topContainer {
    width: 100%;
    height: 250px;
    /* background-color: aquamarine; */
    margin: 20px 0px;
    display: flex;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        width: 100%;
        height: auto;
    }
}


.leftarea {
    width: 31%;
    height: 200px;
    border: 1px dashed gray;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-bottom: 10px;
    }
}

.profiePic {
    width: 104px;
    height: 104px;
    border-radius: 104px;
    border: 1px solid #FFF;


    @media screen and (max-width : 576px) {
        width: 100%;
        display: contents;
    }
}

.profiePic img {

    border-radius: 50%;


}

.changeProfile {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #4153ED;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    margin-top: 10px;

    @media screen and (max-width : 576px) {
        width: 100%;
    }

}

.midarea {
    width: 31%;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.firstName {
    display: flex;
    flex-direction: column;

}

.firstName label {
    color: #000;
    font-family: Montserrat;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    font-family: poppins;
}

.firstNameInput {
    border-radius: 5px;
    background: #F9F9F9;
    height: 40px;
    border: none;
    outline: none;
    font-family: poppins;
    padding-left: 5px;
    display: flex;
}

.buttonWarpper {
    display: flex;
    margin: 20px 0px;
}

.buttonWarpperWeb {
    display: flex;
    margin: 20px 0px;

    @media screen and (max-width : 576px) {
        display: none;
    }
}

.buttonWarpperMob {
    display: none;

    @media screen and (max-width : 576px) {
        width: 100%;
        display: -webkit-inline-box;
        flex-direction: column;
        margin: 20px 0px;
    }

}


.saveChangeBtn {
    display: flex;
    height: 30px;
    padding: 10px 20px;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #4153ED;
    color: #FFF;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    cursor: pointer;

}

.deleteAccBtn {
    display: flex;
    height: 30px;
    padding: 10px 20px;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #FFF5F8;
    color: #F1416C;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    margin-left: 10px;
    cursor: pointer;
}

.rightarea {
    width: 31%;

    @media screen and (max-width : 576px) {

        width: 100%;
    }

}

.bottomConatiner {
    width: 90%;
    height: auto;
    border: 1px dashed gray;
    border-radius: 5px;
    padding: 25px;
    margin: 10px 0px;

    @media screen and (max-width : 576px) {
        width: auto;
        height: auto;
    }
}

.botBody {
    width: 100%;
    display: flex;
    flex-direction: column;

}

.botTitle {
    color: #000;
    font-family: Poppins;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    margin-left: 10px;



}

.botTitle1 {
    color: #000;
    font-family: Poppins;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    margin-left: 10px;
    text-align: center;


}

.botTitle1 {
    color: #000;
    font-family: Poppins;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    margin-left: 10px;
    margin-bottom: 15px;

}

.passChange {
    width: 100%;
    display: flex;
    justify-content: space-around;

    @media screen and (max-width : 576px) {
        width: 100%;
        flex-direction: column;
        align-items: center;
    }
}


.oldpassCont {
    width: 30%;

    @media screen and (max-width : 576px) {
        width: 100%;

    }
}

.oldpassCont label {
    color: #000;
    font-family: Montserrat;
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    font-family: poppins;
}

.oldPass {
    width: 100%;
    border-radius: 5px;
    background: #F9F9F9;
    border: none;
    outline: none;
    height: 40px;
    font-family: poppins;
    padding-left: 5px;
}

.botBtn {
    width: 145px;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    display: flex;
    height: 40px;
    padding: 7px 15px;
    margin-top: 25px;
    border-radius: 5px;
    border: 1px solid #4153ED;

    @media screen and (max-width : 576px) {
        width: 80%;
    }
}

.botBtn1 {
    width: 40%;
    margin: auto;
    align-items: center;
    justify-content: center;
    display: flex;
    height: 40px;
    padding: 7px 15px;
    margin-top: 25px;
    border-radius: 5px;
    border: 1px solid #4153ED;

    @media screen and (max-width : 576px) {
        width: 80%;
    }
}


.botBtn1 button {
    color: #4153ED;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;
}

.botBtn button {
    color: #4153ED;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;
}

.fileInput {
    opacity: 0;
    cursor: pointer;
}

.fileInputLabel {
    position: absolute;
}

/* for the silver status */

.silverWrapper {
    padding: 25px;
    width: 90%;
    margin-top: 50px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    border: 1px solid black;
    position: relative;
    border-radius: 5px;
    margin-bottom: 20px;

    @media screen and (max-width : 576px) {
        width: auto
    }
}

.formWrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }
}

.addressName {
    width: 100%;
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width : 576px) {
        margin-bottom: 5px;
    }
}

.addressNameInput input {
    cursor: pointer;
    background-color: #f9f9f9;
    width: 96%;
    height: 40px;
    padding-left: 10px;
    padding-right: 10px;
    position: relative;
    cursor: pointer;
  
}

.addressNameInput {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #c4c3c3;
    cursor: pointer;
    margin: 10px 0px;

}

.fileUploaded {
    border: 2px solid #76d376;
}

.courtyOptions {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 150px;
    outline: none;
    border: none;
}

.courtyOptions select {

    outline: none;
    border: none;
}

.fileInput[type='file'] {
    opacity: 0;
    cursor: pointer;
}


.upload {
    z-index: -99;
    position: absolute;
    right: 30px;
    top: 12px;
    cursor: default
}

.nextBtnCont {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.nextsubmitBtn {
    background-color: #4153ed;
    padding: 15px 120px;
    color: white;
    gap: 10px;
    font-weight: 600;
    border-color: #4153ed;
    outline: none;
    border-radius: 3px;
    margin-bottom: 30px;
    cursor: pointer;
}


.silverTag {
    width: 300px;
    height: 30px;
    background-color: silver;
    position: absolute;
    top: -15px;
    text-align: center;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;

    @media screen and (max-width : 576px) {
        width: 200px;

    }
}

.silverTagName {

    font-size: 16px;
}

/* gold status */
.goldWrapper {
    padding: 25px;
    width: 90%;
    margin-top: 50px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    border: 1px solid gold;
    position: relative;
    border-radius: 5px;

    @media screen and (max-width : 576px) {
        width: auto;

    }
}

.goldTag {
    width: 200px;
    height: 30px;
    background-color: gold;
    position: absolute;
    top: -15px;
    text-align: center;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
}

.goldPara {
    font-size: 15px;

    @media screen and (max-width : 576px) {
        font-size: 10px;
    }
}

.goldNameTag {
    font-size: 16px;
}


/* gold status */


/* peer pages  */

.peerFormConatiner {
    width: 90%;
    /* height: 200px; */
    border: 1px dashed gray;
    border-radius: 5px;
    padding: 25px;
    margin-bottom: 0px;
    margin-top: 10px;

    @media screen and (max-width : 576px) {
        width: auto;
        height: auto;
    }
}

.formWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;

    @media screen and (max-width: 576px) {
        display: flex;
        flex-direction: column;
        margin-bottom: 0px;
    }
}

.firstName {
    width: 96%;

    @media screen and (max-width: 576px) {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
    }
}

.firstNameC {
    width: 44%;

    @media screen and (max-width: 576px) {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
    }
}


.firstNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    height: 100%;
    padding-left: 10px;
    padding-right: 10px;

    @media screen and (max-width: 576px) {
        width: 90%;
        height: 40px;
        display: flex;
        flex-direction: column;
    }
}

.firstNameInputC input {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;

    @media screen and (max-width: 576px) {
        width: 100%;
        display: flex;
        flex-direction: column;
    }
}





/* peer pages  */


/* authentication */
.submitBtnCont {
    /* display: flex; */
    justify-content: center;
    align-items: center;
    flex-direction: column;
    /* margin-bottom: 20px; */
}

.twoFactorContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0px 0px 0px;
    flex-direction: column;

    @media screen and (max-width: 576px) {
        flex-direction: column;
        width: 100%;

    }
}

.submitBtn {
    margin-top: 20px;
    background-color: #fff;
    padding: 15px 50px;
    gap: 10px;
    color: #4153ed;
    border-color: #4153ed;
    outline: none;
    border-radius: 3px;
    cursor: pointer;
}

.cancelBtn {
    padding: 5px 18px;
    font-family: poppins;
    font-weight: 600;
    border-radius: 2px;
    border: none;
    background-color: #a6a7ad;
    color: #ebebeb;
    cursor: pointer;
    margin: 0px 5px;

    @media screen and (max-width: 576px) {

        width: 100%;
    }
}

.verifyBtn {
    padding: 5px 18px;
    font-family: poppins;
    font-weight: 600;
    border-radius: 2px;
    border: none;
    background-color: #4153ed;
    color: #ebebeb;
    cursor: pointer;
    margin: 0px 5px;

    @media screen and (max-width: 576px) {

        width: 100%;
    }
}

.qrContainet {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.verifyInput {
    padding: 5px 10px;
    margin-right: 10px;
    font-family: poppins;
    margin-bottom: 10px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin-bottom: 20px;
        margin-right: 0px
    }
}

/* authentication */

@media screen and (max-width : 576px) {
    .addressNameInput label {
        padding-left: 20px !important; /* Increased left padding for mobile */
        padding-right: 40px !important; /* Added right padding to prevent overlap with tick mark */
    }

    .upload {
        right: 15px; /* Adjusted right position for mobile */
        top: 50%;    /* Vertically center */
        transform: translateY(-50%);
        height: 15px; /* Explicitly set height if not already inferenceable */
        width: 15px; /* Explicitly set width */
    }
}

.nextBtnCont {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.nextsubmitBtn {
    background-color: #4153ed;
    padding: 15px 120px;
    color: white;
    gap: 10px;
    font-weight: 600;
    border-color: #4153ed;
    outline: none;
    border-radius: 3px;
    margin-bottom: 30px;
    cursor: pointer;
}