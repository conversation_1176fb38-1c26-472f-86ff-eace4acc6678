// import Webcam from "react-webcam";
// import Image from "next/image";
// import { useCallback, useRef, useState } from "react";
// import styles from "./webcam.module.css";

// const CustomWebcam = (props) => {
//   const webcamRef = useRef(null);
//   const [imgSrc, setImgSrc] = useState(null);

//   const capture = useCallback(() => {
//     const imageSrc = webcamRef.current.getScreenshot();
//     setImgSrc(imageSrc);
//   }, [webcamRef]);

//   const retake = () => {
//     setImgSrc(null);
//   };

//   props.pullImageSrc(imgSrc);

//   return (
//     <div className={styles.container}>
//       {imgSrc ? (
//         <Image src={imgSrc} height={300} width={300} alt="webcam" />
//       ) : (
//         <Webcam height={300} width={300} ref={webcamRef} />
//       )}
//       <div className={styles.Btncontainer}>
//         {imgSrc ? (
//           <button className={styles.btn} onClick={retake}>
//             Retake photo
//           </button>
//         ) : (
//           <button className={styles.btn} onClick={capture}>
//             Capture photo
//           </button>
//         )}
//       </div>
//     </div>
//   );
// };

// export default CustomWebcam;

// "use client";
// import React, { useRef, useState } from "react";
// import styles from "./webcam.module.css";
// import Image from "next/image";

// const CustomWebcam = ({ handleKycSelfie }) => {
// const videoRef = useRef(null);
// const canvasRef = useRef(null);
// const [capturedImage, setCapturedImage] = useState(null);
// const [showVideo, setShowVideo] = useState(true);

// const startCamera = async () => {
//   try {
//     const stream = await navigator.mediaDevices.getUserMedia({ video: true });
//     if (videoRef.current) {
//       videoRef.current.srcObject = stream;
//     }
//   } catch (error) {
//     console.error("Error accessing camera:", error);
//   }
// };

// const captureImage = () => {
//   const video = videoRef.current;
//   const canvas = canvasRef.current;
//   if (video && canvas) {
//     const context = canvas.getContext("2d");
//     context.drawImage(video, 0, 0, canvas.width, canvas.height);
//     const dataUrl = canvas.toDataURL("image/jpeg");
//     const imgurl = dataUrl ? dataUrl.split(",")[1] : "no image";
//     setCapturedImage(dataUrl);
//     handleKycSelfie(dataUrl);
//     setShowVideo(false);
//   }
// };

// const retakeImage = () => {
//   setCapturedImage(null);
//   setShowVideo(true);
// };

// const stopCamera = () => {
//   const stream = videoRef.current.srcObject;
//   const tracks = stream.getTracks();
//   tracks.forEach((track) => track.stop());
//   videoRef.current.srcObject = null;
// };

// return (
//   <div className={styles.container}>
//     <div className={styles.btnContainer}>
//       {showVideo && (
//         <>
//           <button className={styles.btn} onClick={startCamera}>
//             Start Camera
//           </button>
//           <button className={styles.btn} onClick={captureImage}>
//             Capture Image
//           </button>
//         </>
//       )}
//     </div>
//     {capturedImage && (
//       <div className={styles.btnContainer}>
//         <button className={styles.btn} onClick={stopCamera}>
//           Stop Camera
//         </button>
//         <button className={styles.btn} onClick={retakeImage}>
//           Retake
//         </button>
//         <img src={capturedImage} alt="Captured" />
//       </div>
//     )}
//     <video
//       className={styles.vidCont}
//       ref={videoRef}
//       autoPlay
//       muted
//       width={340}
//       height={280}
//       style={{ display: showVideo ? "block" : "none" }}
//     />
//     <canvas
//       ref={canvasRef}
//       style={{ display: "none" }}
//       width={340}
//       height={280}
//     />
//   </div>
// );

//   const videoRef = useRef(null);
//   const captureButtonRef = useRef(null);
//   const [capturedBlob, setCapturedBlob] = useState(null);
//   const [showVideo, setShowVideo] = useState(true);
//   const [capImg, setCapImg] = useState([]);
//   let mediaStream;

//   React.useEffect(() => {
//     // Open camera on page load
//     navigator.mediaDevices
//       .getUserMedia({ video: true })
//       .then((stream) => {
//         videoRef.current.srcObject = stream;
//         mediaStream = stream;
//       })
//       .catch((error) => {
//         console.error("Error accessing the camera:", error);
//       });

//     // Stop camera access when the component unmounts
//     return () => {
//       if (mediaStream) {
//         mediaStream.getTracks().forEach((track) => track.stop());
//       }
//     };
//   }, []);

//   // Capture image from video stream and display on canvas
//   const handleCapture = () => {
//     let canvas = document.createElement("canvas");
//     canvas.width = videoRef.current.videoWidth;
//     canvas.height = videoRef.current.videoHeight;
//     let context = canvas.getContext("2d");
//     context.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

//     let dataURL = canvas.toDataURL("image/png");
//     let blobPromise = (async () => {
//       return await fetch(dataURL).then((res) => res.blob());
//     })();

//     blobPromise.then((blob) => {
//       // Optional: Display the captured image on the page
//       let img = document.createElement("img");
//       img.src = URL.createObjectURL(blob);
//       setCapImg(img);
//       setCapturedBlob(blob);
//       handleKycSelfie(blob);
//       setShowVideo(false);
//       // let img = document.createElement("img");
//       // img.src = URL.createObjectURL(blob);
//       // document.body.appendChild(img);
//       // setCapturedBlob(blob);
//       // handleKycSelfie(blob);
//       // setShowVideo(false);

//       // Save blob in state or context if needed
//       // You can use it later to send as a file in the saveImage function
//     });
//   };

//   return (
//     <div className={styles.container}>
//       {!capImg ?  <video
//         ref={videoRef}
//         width={340}
//         height={280}
//         autoPlay
//         style={{ display: showVideo ? "block" : "none" }}
//       ></video> :  <div>
//       <Image src={capImg.src} width={320} height={280} />
//     </div>}
//       <div className={styles.btnContainer}>
//         <>
//           <button className={styles.btn} onClick={handleCapture}>
//             Capture Image
//           </button>
//           {/* <div>
//             {capImg && <Image src={capImg.src} width={320} height={280} />}
//           </div> */}
//         </>
//       </div>
//       {/* <button onClick={handleCapture}>Capture Image</button> */}
//     </div>
//   );
// };

// export default CustomWebcam;
"use client";
import { useState, useRef, useEffect } from "react";
import styles from "./webcam.module.css";
import Image from "next/image";

const CustomWebcam = ({ handleKycSelfie }) => {
  const videoRef = useRef(null);
  const [capturedBlob, setCapturedBlob] = useState(null);
  const [showVideo, setShowVideo] = useState(true);
  const [capImg, setCapImg] = useState(null);
  let mediaStream;

  const handleCapture = () => {
    if (!videoRef.current) {
      console.error("Video element not found");
      return;
    }

    let canvas = document.createElement("canvas");
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    let context = canvas.getContext("2d");
    context.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

    let dataURL = canvas.toDataURL("image/png");
    let blobPromise = (async () => {
      return await fetch(dataURL).then((res) => res.blob());
    })();

    blobPromise.then((blob) => {
      let img = document.createElement("img");
      img.src = URL.createObjectURL(blob);
      setCapImg(img);
      setCapturedBlob(blob);

      handleKycSelfie(blob); // Replace this with your actual function
      setShowVideo(false);
    });
  };

  const handleRetake = () => {
    setCapturedBlob(null);
    setCapImg(null);
    setShowVideo(true);
    startCamera();
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        mediaStream = stream;
        setShowVideo(true); // Ensure video is displayed after starting camera
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
    }
  };

  // Open camera on page load
  useEffect(() => {
    navigator.mediaDevices
      .getUserMedia({ video: true })
      .then((stream) => {
        videoRef.current.srcObject = stream;
        mediaStream = stream;
      })
      .catch((error) => {
        console.error("Error accessing the camera:", error);
      });

    // Stop camera access when the component unmounts
    return () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  return (
    <div className={styles.container}>
      {!capImg ? (
        <video
          ref={videoRef}
          width={340}
          height={280}
          autoPlay
          style={{ display: showVideo ? "block" : "none" }}
        ></video>
      ) : (
        <div>
          <Image src={capImg.src} width={320} height={280} />
        </div>
      )}
      <div className={styles.btnContainer}>
        <>
          {!capturedBlob ? (
            <button className={styles.btn} onClick={handleCapture}>
              Capture Image
            </button>
          ) : (
            <button className={styles.btn} onClick={handleRetake}>
              Retake
            </button>
          )}
        </>
      </div>
    </div>
  );
};

export default CustomWebcam;
