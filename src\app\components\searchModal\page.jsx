"use client";
import { useState } from "react";
import ReactDOM from "react-dom";
import Modal from "react-modal";
import styles from "./pay.module.css";
import Image from "next/image";
import addIcon from "../../../../public/assets/addIcon.svg";
import ewallet from "../../../../public/assets/payments/ewallet.svg";
import Bitcoin from "../../../../public/assets/payments/Bitcoin.png";

import crypto from "../../../../public/assets/payments/crypto.png";
import sepa from "../../../../public/assets/payments/sepa.svg";
import localBank from "../../../../public/assets/payments/bank.png";
import foreignBank from "../../../../public/assets/payments/foreignBank.png";

const page = () => {
  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "15px",
    },
  };
  Modal.setAppElement("body");
  let subtitle;
  const [modalIsOpen, setIsOpen] = useState(false);
  const [payTypeSelected, setPayTypeSelected] = useState(false);

  const handlePayFromClass = () => {
    setPayTypeSelected(false);
  };
  const handlePayToClass = () => {
    setPayTypeSelected(true);
  };

  const openModal = () => {
    setIsOpen(true);
  };

  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
    subtitle.style.color = "#f00";
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  return (
    <>
      <div className={styles.paymentBoxContainer}>
        <div className={styles.paymentHeader}>
          <div className={styles.payHeaderCont}>
            <div className={styles.payHeader}>Select payment Method</div>
            <div className={styles.payAdd_btn}></div>{" "}
            <div>
              <Image
                src={addIcon}
                alt="Picture of the logo"
                width={20}
                height={20}
                onClick={openModal}
              />
              <Modal
                isOpen={modalIsOpen}
                onAfterOpen={afterOpenModal}
                onRequestClose={closeModal}
                style={customStyles}
                contentLabel="Example Modal"
              >
                <h2
                  ref={(_subtitle) => (subtitle = _subtitle)}
                  className={styles.modalHeader}
                >
                  Add new payment method
                  <svg
                    style={{ marginLeft: "30px" }}
                    onClick={closeModal}
                    xmlns="http://www.w3.org/2000/svg"
                    width="15"
                    height="16"
                    viewBox="0 0 15 16"
                    fill="none"
                  >
                    <path
                      d="M8.60216 7.983L14.8197 1.78213C14.9425 1.63904 15.0067 1.45497 14.9994 1.26672C14.9921 1.07847 14.9139 0.899895 14.7803 0.766681C14.6468 0.633468 14.4677 0.555427 14.2789 0.548156C14.0902 0.540885 13.9056 0.604918 13.7622 0.727459L7.54466 6.92833L1.32716 0.719979C1.18593 0.579129 0.994384 0.5 0.794658 0.5C0.594931 0.5 0.403385 0.579129 0.262158 0.719979C0.12093 0.860829 0.0415888 1.05186 0.0415888 1.25105C0.0415888 1.45025 0.12093 1.64128 0.262158 1.78213L6.48716 7.983L0.262158 14.1839C0.183646 14.2509 0.119881 14.3334 0.0748636 14.4262C0.0298461 14.519 0.00454851 14.6201 0.000558893 14.7231C-0.00343073 14.8261 0.0139734 14.9289 0.0516793 15.0249C0.0893852 15.1208 0.146579 15.208 0.21967 15.2809C0.292761 15.3538 0.380171 15.4109 0.476415 15.4485C0.572659 15.4861 0.675658 15.5034 0.778948 15.4994C0.882237 15.4955 0.983586 15.4702 1.07664 15.4253C1.16968 15.3804 1.25242 15.3168 1.31966 15.2385L7.54466 9.03767L13.7622 15.2385C13.9056 15.3611 14.0902 15.4251 14.2789 15.4178C14.4677 15.4106 14.6468 15.3325 14.7803 15.1993C14.9139 15.0661 14.9921 14.8875 14.9994 14.6993C15.0067 14.511 14.9425 14.327 14.8197 14.1839L8.60216 7.983Z"
                      fill="#858585"
                    />
                  </svg>
                  {/* <button>close</button> */}
                </h2>
                {/* <div>I am a modal</div> */}
                <div className={styles.firstName}>
                  <div className={styles.firstNameLabel}>Method Name*</div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="firstname"
                      onChange={(e) => setFirstName(e.target.value)}
                      maxLength={100}
                      // required
                    />
                  </div>
                </div>
                <div className={styles.firstName}>
                  <div className={styles.firstNameLabel}>
                    Country of operation*
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="firstname"
                      onChange={(e) => setFirstName(e.target.value)}
                      maxLength={100}
                      // required
                    />
                  </div>
                </div>
                <div className={styles.firstName}>
                  <div className={styles.firstNameLabel}>Account username*</div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="firstname"
                      onChange={(e) => setFirstName(e.target.value)}
                      maxLength={100}
                      // required
                    />
                  </div>
                </div>
                <div className={styles.firstName}>
                  <div className={styles.firstNameLabel}>Email address*</div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      id="firstname"
                      onChange={(e) => setFirstName(e.target.value)}
                      maxLength={100}
                      // required
                    />
                  </div>
                </div>
                <div>
                  <div className={styles.PayMethodMain}>
                    <div className={styles.paymentBoxWrapper}>
                      Select Payment Method
                    </div>
                    <div className={styles.paymentDirection}>
                      <div
                        onClick={handlePayFromClass}
                        className={`${styles.payFrom} ${
                          !payTypeSelected ? styles.activePaymentDirection : ""
                        }`}
                      >
                        From - <span style={{ color: "blue" }}>Sepa</span>{" "}
                      </div>
                      <div
                        onClick={handlePayToClass}
                        className={`${styles.payTo} ${
                          payTypeSelected ? styles.activePaymentDirection : ""
                        }`}
                      >
                        To
                      </div>
                    </div>
                    <div className={styles.fifthformWrapper}>
                      <div className={styles.paymentGateways}>
                        <Image
                          src={ewallet}
                          alt="Picture of the logo"
                          width={56}
                          height={46}
                        />
                      </div>
                      <div className={styles.BitcoinpaymentGateways}>
                        <Image
                          src={Bitcoin}
                          alt="Picture of the logo"
                          width={30}
                          height={30}
                        />
                        <Image
                          src={crypto}
                          alt="Picture of the logo"
                          width={50}
                          height={25}
                        />
                      </div>
                      <div className={styles.paymentGateways}>
                        <Image
                          src={sepa}
                          alt="Picture of the logo"
                          width={56}
                          height={46}
                        />
                      </div>
                      <div className={styles.paymentGateways}>
                        <Image
                          src={localBank}
                          alt="Picture of the logo"
                          width={56}
                          height={46}
                        />
                      </div>
                      <div className={styles.paymentGateways}>
                        <Image
                          src={foreignBank}
                          alt="Picture of the logo"
                          width={56}
                          height={46}
                        />
                      </div>
                    </div>
                  </div>
                  <button className={styles.payAddBtn}>
                    Send Request To Admin
                  </button>
                </div>
              </Modal>
            </div>
          </div>
        </div>
        <div className={styles.paymentDirection}>
          <div className={styles.payFrom}>
            From - <span style={{ color: "blue" }}>Sepa</span>{" "}
          </div>
          <div className={styles.payTo}> To</div>
        </div>

        <div className={styles.fifthformWrapper}>
          <div className={styles.paymentGateways}>
            <Image
              src={ewallet}
              alt="Picture of the logo"
              width={56}
              height={46}
            />
          </div>
          <div className={styles.BitcoinpaymentGateways}>
            <Image
              src={Bitcoin}
              alt="Picture of the logo"
              width={30}
              height={30}
            />
            <Image
              src={crypto}
              alt="Picture of the logo"
              width={50}
              height={25}
            />
          </div>
          <div className={styles.paymentGateways}>
            <Image
              src={sepa}
              alt="Picture of the logo"
              width={56}
              height={46}
            />
          </div>
          <div className={styles.paymentGateways}>
            <Image
              src={localBank}
              alt="Picture of the logo"
              width={56}
              height={46}
            />
          </div>
          <div className={styles.paymentGateways}>
            <Image
              src={foreignBank}
              alt="Picture of the logo"
              width={56}
              height={46}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default page;
