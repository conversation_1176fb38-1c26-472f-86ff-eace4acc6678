"use client";
import React, { useEffect, useState, useRef } from "react";
import Image from "next/image";
import styles from "./login.module.css";
import line from "../../../../public/line.svg";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import loginApi from "../../api/onboarding/login";
import linkedIn from "../../../../public/assets/socials/linkedin.svg";
import facebook from "../../../../public/assets/socials/facebook.svg";
import Link from "next/link";
import ReCaptcha from "../../components/ReCaptcha/page";
import verifyCaptcha from "@/app/api/onboarding/verifycaptcha";
import { useTimer } from "@/app/context/TimerContext";

require("dotenv").config();

const login = () => {
  const router = useRouter();
  const { startTimer } = useTimer();
  const ref = useRef(null);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [User, setUser] = useState("");
  const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
  const recaptchaRef = useRef(null);
  const [captchaToken, setCaptchatoken] = useState(null);
  const [loginBtnIsActive, setLoginBtnIsActive] = useState(false);

  const handleCaptchaChange = (value) => {
    setIsCaptchaVerified(!!value);
    setCaptchatoken(value);
  };

  const Data = {
    email: email,
    password: password,
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/login/`;
  const CaptchaURL = `${BaseURL}/verifycaptch/`;

  const handleEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setEmail(inputValue);
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setLoginBtnIsActive(true);

    try {
      // Verify captcha
      const verify = await verifyCaptcha(CaptchaURL, captchaToken);
      if (!verify?.data?.success) {
        toast.error("Captcha Not Verified Successfully");
        return;
      }
    } catch (error) {
      console.log("cap", error);
      if (error.response.data.success == false) {
        recaptchaRef?.current?.reset();
        setLoginBtnIsActive(false);
      }
      return;
    }
    try {
      // Attempt login
      const res = await loginApi(URL, Data);
      if (res.status !== 200) {
        toast.error("Login failed.");
        window.grecaptcha.reset();
        return;
      }

      const { two_factor, msg, data } = res.data;

      if (two_factor) {
        localStorage.setItem("userEmail", email);
        toast.success(msg);
        router.push("/sign/2fa");
      } else {
        const {
          user_id,
          firstname,
          lastname,
          tokens: { access, refresh },
          user_email,
          chat_token,
        } = data;

        localStorage.setItem("userID", user_id);
        localStorage.setItem("userName", firstname);
        localStorage.setItem("lastname", lastname);
        localStorage.setItem("user", access);
        localStorage.setItem("refreshToken", refresh);
        localStorage.setItem("userEmail", user_email);
        localStorage.setItem("chatToken", chat_token);
        sessionStorage.setItem("user", access);
        setLoginBtnIsActive(false);

        setTimeout(() => {
          router.push("/pages/searchads");
        }, 1500);

        toast.success(res.data.message);
        startTimer();
      }
    } catch (error) {
      if (error.response) {
        setLoginBtnIsActive(false);
        const { status, data, config } = error.response;

        if (config.url === CaptchaURL && status === 400) {
          toast.error("Something went wrong please reload the page.");
        } else if (status === 401 || status === 400) {
          toast.error("Invalid Credentials.");
        } else {
          toast.error("An unexpected error occurred.");
        }
      } else {
        toast.error("An unexpected error occurred.");
      }
      console.error("Error:", error);
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          <h1 className={styles.heading}>Login</h1>
          <div className={styles.subHeading}>
            Enter your credentials to access your account
          </div>

          <div className={styles.orContainer}>
            <div className={styles.line1}>
              <Image src={line} alt="line" />
            </div>
            {/* <div className={styles.or}>or</div> */}
            <div className={styles.line2}>
              <Image src={line} alt="line" />
            </div>
          </div>

          <form action="" onSubmit={onSubmit}>
            <div className={styles.emailContainer}>
              <div className={styles.email}>
                <label className={styles.nameLabel} htmlFor="email">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  maxLength={260}
                  value={email}
                  onChange={handleEmail}
                  required
                  // autoComplete="off"
                />
              </div>
            </div>
            <div className={styles.passwordContainer}>
              {/* <a className={styles.forgotpass} href="/sign/forgotPassword">
                Forgot password ?
              </a> */}
              <Link className={styles.forgotpass} href="/sign/forgotPassword">
                Forgot password ?
              </Link>
              <div className={styles.password}>
                <label className={styles.nameLabel} htmlFor="password">
                  password
                </label>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  maxLength={260}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  // autoComplete="off"
                />

                <span
                  className={styles.hidePass}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {!showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
              </div>
            </div>

            <div className={styles.captchaCont}>
              <ReCaptcha onChange={handleCaptchaChange} ref={recaptchaRef} />
            </div>

            <div className={styles.loginBtnContainer}>
              <button
                type="submit"
                className={styles.loginBtn}
                disabled={loginBtnIsActive}
              >
                Login
              </button>
            </div>
          </form>
          <ToastContainer />
          <div className={styles.lastPart}>
            You don't have an account ?{" "}
            <span>
              {" "}
              <a className={styles.signUp} href="/">
                Register{" "}
              </a>{" "}
            </span>
            <div className={styles.baseLinksContainer}>
              <div className={styles.baseLinks}>
                <a href="">About Us</a>
              </div>
              <div className={styles.baseLinks}>
                {" "}
                <div className={styles.baseLinks}>
                  <a href="">Conatct Us</a>
                </div>
              </div>
              <div className={styles.baseLinks}>
                {" "}
                <div className={styles.baseLinks}>
                  <a href="">FAQ</a>
                </div>
              </div>
              <div className={styles.baseLinks}>
                <a href="">Privacy Policy</a>
              </div>
              <div className={styles.baseLinks}>
                <a href="">Affiliation Partner</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.rightContainer}>
        <div className={styles.rightBody}>
          {/* Security Badge */}
          <div className={styles.securityBadge}>
            Secured Platform
          </div>
          
          {/* Animated Illustration */}
          <div className={styles.illustrationContainer}>
            <div className={styles.globalNetwork}>
              {/* Network Nodes */}
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              
              {/* Connection Lines */}
              <div className={styles.connectionLine}></div>
              <div className={styles.connectionLine}></div>
              <div className={styles.connectionLine}></div>
              
              {/* Money Symbols */}
              <div className={styles.moneySymbol}>$</div>
              <div className={styles.moneySymbol}>€</div>
              <div className={styles.moneySymbol}>£</div>
              
              {/* Security Shield */}
              <div className={styles.securityShield}>
                <svg viewBox="0 0 24 24">
                  <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1ZM10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                </svg>
              </div>
              
              {/* Floating Currency Symbols */}
              <div className={styles.currencyFloat}>¥</div>
              <div className={styles.currencyFloat}>₹</div>
              <div className={styles.currencyFloat}>₦</div>
            </div>
          </div>
          
          <div className={styles.textContainer}>
            <div>
              <div className={styles.firstLine}>
                Secure Global Money Transfers
              </div>
              <div className={styles.secondLine}>
                Join thousands of users who trust Remflow for fast, secure, and compliant cross-border payments. Our platform connects you with verified traders worldwide.
              </div>
              <div className={styles.lastLine}>
                Experience seamless currency exchange with real-time rates, end-to-end encryption, and 24/7 transaction monitoring.
              </div>
              
              {/* Feature Highlights */}
              <div className={styles.featureHighlights}>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM10 17L6 13L7.41 11.59L10 14.17L16.59 7.58L18 9L10 17Z"/>
                    </svg>
                  </div>
                  <span>Bank-level security & encryption</span>
                </div>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.59 10.75C10.21 10.28 9.69 10 9 10C7.9 10 7 10.9 7 12S7.9 14 9 14C10.1 14 11 13.1 11 12C11 11.31 10.72 10.79 10.25 10.41L15.83 4.83L18.5 7.5L17 9H21ZM12 16C13.1 16 14 16.9 14 18C14 19.1 13.1 20 12 20C10.9 20 10 19.1 10 18C10 16.9 10.9 16 12 16Z"/>
                    </svg>
                  </div>
                  <span>Global network of verified traders</span>
                </div>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M13 9H11V7H13M13 17H11V11H13M12 2A10 10 0 0 0 2 12A10 10 0 0 0 12 22A10 10 0 0 0 22 12A10 10 0 0 0 12 2Z"/>
                    </svg>
                  </div>
                  <span>Real-time transaction tracking</span>
                </div>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 3C7.58 3 4 6.58 4 11C4 14.17 6.05 16.9 8.91 17.71L12 22L15.09 17.71C17.95 16.9 20 14.17 20 11C20 6.58 16.42 3 12 3ZM12 13C10.9 13 10 12.1 10 11C10 9.9 10.9 9 12 9C13.1 9 14 9.9 14 11C14 12.1 13.1 13 12 13Z"/>
                    </svg>
                  </div>
                  <span>Available in 100+ countries</span>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className={styles.trustIndicators}>
                <div className={styles.trustItem}>
                  <div className={styles.trustNumber}>99.9%</div>
                  <div className={styles.trustLabel}>Uptime</div>
                </div>
                <div className={styles.trustItem}>
                  <div className={styles.trustNumber}>$50M+</div>
                  <div className={styles.trustLabel}>Processed</div>
                </div>
                <div className={styles.trustItem}>
                  <div className={styles.trustNumber}>50K+</div>
                  <div className={styles.trustLabel}>Users</div>
                </div>
              </div>
            </div>
            
            {/* bottom section */}
            <div className={styles.bottomSection}>
              <div className={styles.socialLinksCont}>
                <a
                  href="https://www.facebook.com/remflw"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    width={20}
                    height={20}
                    src={facebook}
                    alt="Facebook"
                  />
                </a>
                <a
                  href="https://www.linkedin.com/company/remflow/"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    width={20}
                    height={20}
                    src={linkedIn}
                    alt="LinkedIn"
                  />
                </a>
              </div>
            </div>
            {/* bottom section */}

            {/* mobile tooltip */}

            {/* mobile tooltip */}
          </div>
        </div>
      </div>
    </main>
  );
};

export default login;
