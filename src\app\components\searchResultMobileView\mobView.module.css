@media screen and (max-width: 576px) {
    .container {
        width: 100%;
        border-top: 1px dashed gray;
        border-bottom: 1px dashed gray;
        height: auto;

    }

    .firstRow {


        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        margin-top: 20px;
    }

    .sell {
        color: #50CD89;
        font-family: Poppins;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;

    }

    .price {
        color: #000;
        font-family: Poppins;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;

    }

    .bodyWrapper {
        color: #000;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 300;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;


    }

    .liquidityWrapper {}

    .currencyWrapper {
        width: 150px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .name {
        width: 143px;
        display: flex;
        align-items: center;
        color: #000;
        font-family: Poppins;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        margin-top: 8px;
        margin-bottom: 2px;
        /* fallback for old browsers */
        background: -webkit-linear-gradient(to right, #EF629F, #EECDA3);
        /* Chrome 10-25, Safari 5.1-6 */
        background: linear-gradient(to right, #EF629F, #EECDA3);
        /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */


    }

    .orderInfo {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        /* margin: 3px 0; */
        color: #7D7D7D;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
    }

    .buyBtnWrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #b2eecf;
        border-radius: 5px;
        height: 40px;
        margin: 10px 0px;
    }


    .buyBtn {
        color: #50CD89;
        padding: 10px 20px;
        background-color: transparent;
        border: none;
        font-weight: 600;
        font-family: poppins;
        cursor: pointer;
        font-size: 14px;
    }

    .bottomCont {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .bottomWrapper {
        display: flex;
        flex-direction: column;
    }

    .orderCompleteion {
        margin: 0px 2px;
    }

    .detailsInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        /* margin: 3px 0; */
        color: #7D7D7D;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
    }

    .subClassInfo {
        display: flex;
    }

    .order {
        margin-right: 10px;
    }

    /* modal new */

    .modalPic {
        display: flex;
        justify-content: space-between;
    }

    .modalName {
        color: #000
    }

    .orderContaier {
        display: flex;
        font-size: 14px;
        align-items: center;
        justify-content: flex-start;

        margin: 12px 0px 0px 10px;
    }

    .orders {
        margin-right: 5px;
    }

    .info {
        border: 1px dashed blueviolet;
        padding: 10px 11px;
        border-radius: 5px;
        font-family: poppins;
        margin: 10px 0px;
    }

    .infoPoints {
        margin: 10px 0px;
    }

    .validationCheck {
        color: red;
        font-size: 12px;
        font-family: poppins;
        font-weight: 600;
    }

    .payInput {
        display: flex;
        height: 40px;
        padding: 10px;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        background: #F9F9F9;

    }

    .paySelect {
        width: 70px;
        color: #000;
        font-family: Poppins;
        font-size: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
        height: 100%;
        background-color: transparent;
        border: 1px solid #BABABA;
    }

    .payInput input {
        outline: none;
        border: none;
        height: 100%;
        color: #000;
        font-family: Poppins;
        font-size: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
        background-color: transparent;
    }

    .receive {
        outline: none;
        border: none;
        /* height: 100%; */
        color: #000;
        font-family: Poppins;
        font-size: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
        background-color: transparent;
    }

    .buttonBBUY {
        margin-top: 20px;
        display: flex;
        width: 93%;
        height: 40px;
        padding: 8px 10px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 5px;
        background: #E8FFF3;
        color: #50CD89;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        cursor: pointer;
        /* 91.667% */
    }

    .terms {
        margin: 20px 0px;
    }
}

.status {
    width: 200px;
    /* color: rgb(209, 179, 7); */
    font-size: 14px;
    font-weight: 600;
    font-style: italic;
    /* background: #EECDA3; */
    /* fallback for old browsers */
    background: -webkit-linear-gradient(to right, #EF629F, #EECDA3);
    /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to right, #EF629F, #EECDA3);
    /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.grey {
    color: #7D7D7D;
}

.rateHighlight {
    font-size: 15px;
    font-family: 'Poppins';
    font-weight: 600;
    color: hsl(39deg 100% 51.66%);
}

.payinVerticalLine {
    width: 5px;
    height: 10px;
    background-color: #50CD89;
    border-radius: 5px;
    margin-left: 3px;
}

.payinVerticalLine1 {
    width: 5px;
    height: 10px;
    background-color: #eed027;
    border-radius: 5px;
    margin-left: 3px;
}

.payinVerticalLine2 {
    display: inline-block;
    width: 5px;
    height: 10px;
    background-color: #4153ED;
    border-radius: 5px;
    margin-left: 3px;
}

.payoutOption {
    font-weight: 500;
    color: #7D7D7D;
    width: 100%;
    font-weight: 500;
    color: gray;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 11px;
}