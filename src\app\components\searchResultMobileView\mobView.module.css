@media screen and (max-width: 576px) {
    .container {
        width: 98%;
        margin: 0 auto 16px auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #e2e8f0;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .container:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 16px 16px 0 0;
    }

    /* Trader Header Section */
    .name {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f1f5f9;
    }

    .upload {
        border-radius: 50%;
        border: 2px solid #e2e8f0;
        object-fit: cover;
    }

    .name span {
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        flex: 1;
    }

    /* Listing ID */
    .detailsInfo {
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        color: #64748b;
        margin-bottom: 12px;
        font-weight: 500;
        text-align: center;
        background: #f8fafc;
        padding: 8px 12px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    /* Stats Section */
    .orderInfo {
        margin-bottom: 16px;
    }

    .subClassInfo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
    }

    .order {
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        color: #64748b;
        font-weight: 500;
        background: #f1f5f9;
        padding: 6px 12px;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
    }

    .orderCompleteion {
        display: flex;
        align-items: center;
        gap: 6px;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-weight: 600;
        color: #059669;
        background: #d1fae5;
        padding: 6px 10px;
        border-radius: 6px;
        border: 1px solid #a7f3d0;
    }

    .orderCompleteion svg {
        width: 12px;
        height: 12px;
    }

    /* Main Content Section */
    .bodyWrapper {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 20px;
    }

    /* Rate Highlight */
    .rateHighlight {
        font-family: 'Poppins', sans-serif;
        font-size: 18px;
        font-weight: 700;
        color: #0f172a;
        text-align: center;
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        padding: 12px 16px;
        border-radius: 12px;
        border: 2px solid #f59e0b;
        margin-bottom: 8px;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
    }

    /* Liquidity Info */
    .liquidityWrapper {
        display: flex;
        flex-direction: column;
        gap: 8px;
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }

    .liquidityWrapper > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #1e293b;
    }

    .grey {
        color: #64748b;
        font-weight: 400;
    }

    /* Currency and Payment Methods */
    .currencyWrapper {
        display: flex;
        flex-direction: column;
        gap: 12px;
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }

    .currencyWrapper > div:first-child {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #475569;
        padding: 8px 12px;
        background: white;
        border-radius: 8px;
        border: 1px solid #d1d5db;
    }

    .currencyWrapper > div:first-child svg {
        width: 12px;
        height: 12px;
    }

    .payoutOption {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: 'Poppins', sans-serif;
        font-size: 13px;
        font-weight: 500;
        color: #64748b;
        padding: 10px 12px;
        background: white;
        border-radius: 8px;
        border: 1px solid #d1d5db;
        margin-bottom: 4px;
    }

    /* Bottom Section */
    .bottomCont {
        width: 100%;
        margin-top: 8px;
    }

    .buyBtnWrapper {
        width: 100%;
        background: linear-gradient(135deg, #10b981, #059669);
        border-radius: 12px;
        padding: 0;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .buyBtnWrapper:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    .buyBtn {
        width: 100%;
        color: white;
        padding: 14px 20px;
        background: transparent;
        border: none;
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    /* Modal Styles */
    .modalCont {
        max-width: 95vw;
        width: 100%;
        background: white;
        border-radius: 16px;
        padding: 0;
        outline: none;
        border: none;
        overflow: hidden;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .modalTop {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-bottom: 1px solid #e2e8f0;
    }

    .left {
        display: flex;
        flex-direction: column;
        flex: 1;
    }

    .modalProfileWrapper {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .modalPic {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .modalPic > div:first-child {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .modalName {
        font-family: 'Poppins', sans-serif;
        font-size: 18px;
        font-weight: 700;
        color: #1e293b;
    }

    .right {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: #f1f5f9;
        transition: all 0.2s ease;
        cursor: pointer;
        flex-shrink: 0;
    }

    .right:hover {
        background: #e2e8f0;
        transform: scale(1.05);
    }

    .orderContaier {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Poppins', sans-serif;
        font-size: 13px;
        color: #64748b;
        margin-top: 8px;
    }

    .orders {
        display: flex;
        align-items: center;
        gap: 4px;
        background: #f1f5f9;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
    }

    .modalBottom {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .leftB {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .info {
        display: flex;
        flex-direction: column;
        gap: 12px;
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }

    .infoPoints {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Poppins', sans-serif;
        font-size: 13px;
        color: #475569;
        line-height: 1.4;
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
    }

    .infoPoints:last-child {
        border-bottom: none;
    }

    .highlight {
        font-weight: 600;
        color: #1e293b;
    }

    .rightB {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .validationCheck {
        color: #ef4444;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-weight: 600;
        margin-top: 4px;
        min-height: 16px;
    }

    .payInput {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 14px 16px;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        background: white;
        transition: all 0.2s ease;
        font-family: 'Poppins', sans-serif;
    }

    .payInput:focus-within {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .payInput input {
        flex: 1;
        border: none;
        outline: none;
        font-size: 14px;
        color: #1e293b;
        font-family: 'Poppins', sans-serif;
        background: transparent;
    }

    .payInput input::placeholder {
        color: #9ca3af;
    }

    .receive {
        flex: 1;
        font-size: 14px;
        color: #1e293b;
        font-family: 'Poppins', sans-serif;
    }

    .buttonBBUY {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 16px 24px;
        border-radius: 12px;
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .buttonBBUY:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    .terms {
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }

    .tHeader {
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 8px;
    }

    .termsPara {
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        color: #64748b;
        line-height: 1.5;
    }
}

/* Payment Method Indicators */
.payinVerticalLine {
    width: 6px;
    height: 12px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 3px;
    margin-left: 8px;
    flex-shrink: 0;
}

.payinVerticalLine1 {
    width: 6px;
    height: 12px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 3px;
    margin-left: 8px;
    flex-shrink: 0;
}

.payinVerticalLine2 {
    width: 6px;
    height: 12px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 3px;
    margin-left: 8px;
    flex-shrink: 0;
}

/* Additional Mobile Responsive Styles */
@media screen and (max-width: 480px) {
    .container {
        width: 96%;
        padding: 16px;
        margin-bottom: 12px;
    }

    .name span {
        font-size: 15px;
    }

    .rateHighlight {
        font-size: 16px;
        padding: 10px 14px;
    }

    .liquidityWrapper,
    .currencyWrapper {
        padding: 14px;
    }

    .buyBtn {
        padding: 12px 16px;
        font-size: 15px;
    }

    .modalCont {
        max-width: 98vw;
    }

    .modalTop,
    .modalBottom {
        padding: 16px;
    }

    .buttonBBUY {
        padding: 14px 20px;
        font-size: 15px;
    }
}

@media screen and (max-width: 360px) {
    .container {
        width: 95%;
        padding: 14px;
    }

    .name span {
        font-size: 14px;
    }

    .rateHighlight {
        font-size: 15px;
        padding: 8px 12px;
    }

    .liquidityWrapper > div,
    .currencyWrapper > div:first-child {
        font-size: 13px;
    }

    .payoutOption {
        font-size: 12px;
    }

    .buyBtn {
        font-size: 14px;
    }
}

/* Accessibility and Animation Preferences */
@media (prefers-reduced-motion: reduce) {
    .container,
    .buyBtnWrapper,
    .buttonBBUY,
    .right {
        transition: none;
        animation: none;
    }

    .container:hover,
    .buyBtnWrapper:hover,
    .buttonBBUY:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .container {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .container::before {
        background: #000;
    }

    .name span {
        color: #000;
    }

    .detailsInfo {
        background: #fff;
        border: 1px solid #000;
        color: #000;
    }

    .buyBtnWrapper {
        background: #000;
    }

    .buyBtn {
        color: #fff;
    }
}