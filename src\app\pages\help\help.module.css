/* Main Container */
.rightContainerBody {
    width: 100%;
    height: 100%;
    padding: 30px;
    border-radius: 20px;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: calc(100vh - 120px);
}

@media screen and (max-width: 576px) {
    .rightContainerBody {
        padding: 15px 5px;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
    }
}

/* Help Container */
.helpContainer {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

@media screen and (max-width: 576px) {
    .helpContainer {
        width: 98%;
        gap: 20px;
    }
}

/* Mobile Header Section */
.mobileHeader {
    display: none;
}

@media (max-width: 576px) {
    .mobileHeader {
        display: block;
        margin-bottom: 20px;
        padding: 0 16px;
        text-align: center;
    }
}

.headerContent {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.pageTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

@media screen and (max-width: 576px) {
    .pageTitle {
        font-size: 24px;
        text-align: center;
        width: 100%;
    }
}

.pageSubtitle {
    font-size: 14px;
    color: #64748B;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

@media (max-width: 576px) {
    .pageSubtitle {
        font-size: 13px;
    }
}

/* Welcome Section */
.welcomeSection {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .welcomeSection {
        padding: 25px 20px;
        border-radius: 16px;
    }
}

.welcomeIcon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.8;
}

@media screen and (max-width: 576px) {
    .welcomeIcon {
        font-size: 48px;
        margin-bottom: 15px;
    }
}

.welcomeTitle {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 15px 0;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

@media screen and (max-width: 576px) {
    .welcomeTitle {
        font-size: 24px;
        margin-bottom: 12px;
    }
}

.welcomeDescription {
    font-size: 16px;
    color: #64748b;
    line-height: 1.6;
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

@media screen and (max-width: 576px) {
    .welcomeDescription {
        font-size: 14px;
        line-height: 1.5;
    }
}

/* FAQ Section */
.faqSection {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .faqSection {
        padding: 20px 15px;
        border-radius: 16px;
    }
}

.faqTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 25px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

@media screen and (max-width: 576px) {
    .faqTitle {
        font-size: 20px;
        margin-bottom: 20px;
        justify-content: center;
        text-align: center;
    }
}

.faqIcon {
    font-size: 24px;
    opacity: 0.8;
}

/* Accordion Styles */
.accordionContainer {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.accordionItem {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.accordionItem:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.accordionHeader {
    width: 100%;
    padding: 20px 24px;
    background: transparent;
    border: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    font-family: inherit;
    transition: all 0.3s ease;
}

@media screen and (max-width: 576px) {
    .accordionHeader {
        padding: 16px 18px;
        gap: 12px;
    }
}

.accordionHeader:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.accordionHeaderActive {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.accordionHeaderActive:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.accordionQuestion {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    flex: 1;
}

@media screen and (max-width: 576px) {
    .accordionQuestion {
        font-size: 14px;
        line-height: 1.3;
    }
}

.accordionIcon {
    font-size: 14px;
    transition: transform 0.3s ease;
    opacity: 0.7;
    flex-shrink: 0;
}

.accordionIconRotated {
    transform: rotate(180deg);
}

.accordionContent {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.accordionContentActive {
    max-height: 500px;
    border-top: 1px solid #e2e8f0;
}

.accordionAnswer {
    padding: 20px 24px;
    font-size: 15px;
    line-height: 1.6;
    color: #475569;
}

@media screen and (max-width: 576px) {
    .accordionAnswer {
        padding: 16px 18px;
        font-size: 14px;
        line-height: 1.5;
    }
}

/* Contact Section */
.contactSection {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.contactCard {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    max-width: 500px;
    width: 100%;
    transition: all 0.3s ease;
}

@media screen and (max-width: 576px) {
    .contactCard {
        padding: 25px 20px;
        border-radius: 16px;
        width: 98%;
        margin: 0 auto;
    }
}

.contactCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.contactIcon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.8;
}

@media screen and (max-width: 576px) {
    .contactIcon {
        font-size: 40px;
        margin-bottom: 15px;
    }
}

.contactTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 12px 0;
}

@media screen and (max-width: 576px) {
    .contactTitle {
        font-size: 20px;
        margin-bottom: 10px;
    }
}

.contactDescription {
    font-size: 16px;
    color: #64748b;
    line-height: 1.6;
    margin: 0 0 25px 0;
}

@media screen and (max-width: 576px) {
    .contactDescription {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 20px;
    }
}

.contactButton {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    outline: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 16px;
    padding: 14px 28px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0 auto;
    font-family: inherit;
}

@media screen and (max-width: 576px) {
    .contactButton {
        width: 100%;
        font-size: 14px;
        padding: 12px 24px;
    }
}

.contactButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.contactButton:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.buttonIcon {
    font-size: 16px;
}

@media screen and (max-width: 576px) {
    .buttonIcon {
        font-size: 14px;
    }
}

/* Accessibility Improvements */
.accordionHeader:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.contactButton:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

/* Animation for smooth accordion transitions */
@keyframes accordionSlideDown {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 500px;
        opacity: 1;
    }
}

@keyframes accordionSlideUp {
    from {
        max-height: 500px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

.accordionContentActive {
    animation: accordionSlideDown 0.3s ease-out;
}

/* Responsive adjustments for very small screens */
@media screen and (max-width: 320px) {
    .rightContainerBody {
        padding: 10px 3px;
    }

    .helpContainer {
        width: 100%;
        gap: 15px;
    }

    .welcomeSection,
    .faqSection,
    .contactCard {
        padding: 20px 15px;
        border-radius: 12px;
    }

    .accordionHeader {
        padding: 14px 16px;
    }

    .accordionAnswer {
        padding: 14px 16px;
    }
}