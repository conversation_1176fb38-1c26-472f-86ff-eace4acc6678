"use client";
import { useState } from "react";
import styles from "./twoFacor.module.css";
import { submitQRToken } from "@/app/api/2fa/generateToken";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { useTimer } from "@/app/context/TimerContext";

const page = () => {
  const router = useRouter();
  const { startTimer } = useTimer();
  if (typeof window !== "undefined") {
    var userEmail = localStorage.getItem("userEmail");
  }

  const [secOtp, setSecOtp] = useState("");
  const verifyQRTokenFn = async (e) => {
    e.preventDefault();
    try {
      const res = await submitQRToken(secOtp, userEmail);

      if (res.status === 200) {
        toast.success("2FA authencation successfull");
        localStorage.setItem("userID", res.data.data.user_id);
        localStorage.setItem("userName", res.data.data.firstname);
        localStorage.setItem("user", res.data.data.tokens.access);
        localStorage.setItem("userEmail", res.data.data.user_email);
        localStorage.setItem("chatToken", res.data.data.chat_token);

        sessionStorage.setItem("user", res.data.data.tokens.access);
        localStorage.setItem("refreshToken", res.data.data.tokens.refresh);
        localStorage.setItem("verificationStatus", res.data.data.flag);

        setTimeout(function () {
          router.push("/pages/searchads");
        }, 1500);
        startTimer();
      }
    } catch (error) {
      toast.error(error.response.data.message);
      console.error(error);
    }
  };

  return (
    <div className={styles.bodyWrapper}>
      <div className={styles.container}>
        <div className={styles.title}>Remflow</div>
        <div className={styles.subTitle}>2FA authentication</div>

        <div className={styles.email}>
          <label className={styles.nameLabel} htmlFor="email">
            Please enter the authentication code to sign In
          </label>
          <input
            type="email"
            id="email"
            maxLength={260}
            // value={email}
            onChange={(e) => setSecOtp(e.target.value)}
            placeholder="Enter your authentication OTP from the App"
            required
          />
        </div>
        <div className={styles.loginBtnContainer}>
          <button
            type="submit"
            className={styles.loginBtn}
            onClick={verifyQRTokenFn}
          >
            Submit
          </button>
        </div>
        <ToastContainer />
      </div>
    </div>
  );
};

export default page;
