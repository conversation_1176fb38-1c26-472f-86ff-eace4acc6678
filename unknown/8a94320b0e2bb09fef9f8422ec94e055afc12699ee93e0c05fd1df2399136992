"use client";
import React, { useState } from "react";
import styles from "./tradeStepper.module.css";
import { Stepper } from "react-form-stepper";

const Page = ({ activeStep }) => {
  // const [activeStep, setActiveStep] = useState(2);
  // console.log("activeStep", activeStep);
  return (
    <Stepper
      className={styles.stepperCont}
      stepsize={"4em"}
      steps={[
        { label: "Trade Accepted" },
        { label: "Payment made to peer" },
        { label: "Payment received by peer" },
        { label: "Payment made to sender" },
        { label: "Payment received by sender" },
      ]}
      activeStep={activeStep}
      connectorStateColors={true}
      disabledColor="#bdbdbd"
      activeColor={"#ed1d24"}
    />
  );
};

export default Page;
