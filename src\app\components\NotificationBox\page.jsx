"use client";
import React, { useEffect, useRef, useState } from "react";
import styles from "./notificationBox.module.css";
import NotificationItem from "../../components/NotificationList/page";
import { BsBell } from "react-icons/bs";
import {
  getAllNotificationApi,
  clearAllNotificationsApi,
  getAllUnreadNotificationApi,
} from "@/app/api/notifications/notificationApi";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { usePathname } from "next/navigation";
import { useWebsocketContext } from "@/app/context/AuthContext";

const page = (props) => {
  const pathname = usePathname();
  const [selectedCard, setSelectedCard] = useState(null);
  const [notificationsArr, setNotificationsArr] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);

  const authTokenRef = useRef();
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  const { lastJsonMessage } = useWebsocketContext();
  const dataValue = lastJsonMessage;

  const getAllUnreadNotifications = async () => {
    try {
      const res = await customFetchWithToken.get("/get-notification/");

      setNotificationCount(res.data.count);
      const tempNotificationArr = res.data.data;
      const reversedNotifications = [...tempNotificationArr].reverse();

      setNotificationsArr(reversedNotifications);
    } catch (error) {
      console.log(error);
    }
  };

  const getAllUnreadNotificationsCount = async () => {
    try {
      const res = await customFetchWithToken.get("/get-notification/");

      setNotificationCount(res.data.count);
    } catch (error) {
      console.log(error);
    }
  };

  const clearAllNotifications = async () => {
    try {
      const res = await customFetchWithToken.put("/clear-all-notification/");

      getAllUnreadNotificationsCount();
      // setNotificationsArr(res.data.data);
    } catch (error) {
      console.log(error);
    }
  };
  const handleNotificationRemove = (id) => {
    let removeIDCLicked = id;
    setSelectedCard(id);
    getAllUnreadNotificationsCount();
  };

  if (pathname === "/pages/searchads" && !token) {
    return;
  } else {
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        getAllUnreadNotifications();
      }, 100);
      return () => {
        clearTimeout(timeoutId); // Clear the timeout properly
      };
    }, []);
  }

  return (
    <div className={styles.rightContainerBell}>
      <button
        type="button"
        className={styles.icon_button}
        onClick={props.handleNotificationDrop}
      >
        <span className={styles.material_icons}>
          <BsBell size={20} />
        </span>
        <span className={styles.icon_button__badge}>{notificationCount}</span>
      </button>
      <div className={styles.notificationContainer}>
        {props.notificationDrop ? (
          <div
            key={Math.floor(Math.random() * 80)}
            className={styles.notificationLists}
          >
            <div
              className={styles.clearNotificationsCont}
              onClick={handleNotificationRemove}
            >
              {!selectedCard ? (
                <div
                  className={styles.clearNotificationsBtn}
                  onClick={clearAllNotifications}
                >
                  Clear Notifications
                </div>
              ) : (
                <div className={styles.clearNotificationsBtn}>
                  No New Notifications
                </div>
              )}
            </div>
            {notificationsArr.length > 0 ? (
              notificationsArr.map((el, index) => (
                <NotificationItem
                  key={el.id}
                  msg={el?.notifiaction_msg}
                  created_date={el?.created_date}
                  id={el?.id}
                  orderid={el?.notification_json?.order_id}
                  type={el?.type}
                />
              ))
            ) : (
              <div className={styles.noNotifications}>
                You have no new notifications.
              </div>
            )}
          </div>
        ) : (
          ""
        )}
      </div>
    </div>
  );
};

export default page;
