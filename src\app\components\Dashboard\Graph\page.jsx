"use client";
import React, { useEffect, useState, useRef } from "react";
import Chart from "chart.js";
import styles from "./graph.module.css";

const TransactionGraph = () => {
  const [timeRange, setTimeRange] = useState("weekly");
  const [isLoading, setIsLoading] = useState(false);
  const chartRef = useRef(null);
  const canvasRef = useRef(null);

  useEffect(() => {
    const updateGraph = () => {
      setIsLoading(true);

      // Simulate loading delay for smooth UX
      setTimeout(() => {
        let data, labels, gradientColors;

        switch (timeRange) {
          case "monthly":
            data = [2300, 3780, 4560, 5340, 6000, 7690, 6543];
            labels = ["Week 1", "Week 2", "Week 3", "Week 4", "Week 5", "Week 6", "Week 7"];
            gradientColors = ["#3B82F6", "#10B981"];
            break;
          case "yearly":
            data = [23000, 37800, 45600, 53400, 60000, 76430, 43210];
            labels = ["Q1", "Q2", "Q3", "Q4", "Q5", "Q6", "Q7"];
            gradientColors = ["#8B5CF6", "#EC4899"];
            break;
          default:
            data = [230, 378, 456, 534, 600, 456, 497];
            labels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
            gradientColors = ["#10B981", "#3B82F6"];
        }

        const ctx = canvasRef.current?.getContext("2d");
        if (!ctx) return;

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, gradientColors[0]);
        gradient.addColorStop(1, gradientColors[1]);

        // Create hover gradient
        const hoverGradient = ctx.createLinearGradient(0, 0, 0, 300);
        hoverGradient.addColorStop(0, "#F59E0B");
        hoverGradient.addColorStop(1, "#EF4444");

        const config = {
          type: "bar",
          data: {
            labels: labels,
            datasets: [
              {
                label: "Transaction Volume",
                backgroundColor: gradient,
                hoverBackgroundColor: hoverGradient,
                borderColor: "transparent",
                borderWidth: 0,
                data: data,
                borderRadius: 8,
                borderSkipped: false,
                barThickness: 24,
                maxBarThickness: 32,
              },
            ],
          },
          options: {
            maintainAspectRatio: false,
            responsive: true,
            animation: {
              duration: 1200,
              easing: 'easeInOutQuart',
            },
            interaction: {
              intersect: false,
              mode: 'index',
            },
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                titleColor: '#1E293B',
                bodyColor: '#64748B',
                borderColor: 'rgba(59, 130, 246, 0.2)',
                borderWidth: 1,
                cornerRadius: 12,
                displayColors: false,
                titleFont: {
                  size: 14,
                  weight: '600',
                },
                bodyFont: {
                  size: 13,
                  weight: '500',
                },
                padding: 12,
                callbacks: {
                  title: function(context) {
                    return context[0].label;
                  },
                  label: function(context) {
                    return `$${context.parsed.y.toLocaleString()}`;
                  }
                }
              },
            },
            scales: {
              x: {
                display: true,
                grid: {
                  display: false,
                },
                border: {
                  display: false,
                },
                ticks: {
                  color: '#64748B',
                  font: {
                    size: 12,
                    weight: '500',
                  },
                  padding: 8,
                },
              },
              y: {
                display: true,
                grid: {
                  color: 'rgba(148, 163, 184, 0.1)',
                  drawBorder: false,
                },
                border: {
                  display: false,
                },
                ticks: {
                  color: '#64748B',
                  font: {
                    size: 11,
                    weight: '500',
                  },
                  padding: 12,
                  callback: function(value) {
                    return '$' + value.toLocaleString();
                  }
                },
              },
            },
          },
        };

        // Destroy existing chart
        if (chartRef.current) {
          chartRef.current.destroy();
        }

        // Create new chart
        chartRef.current = new Chart(ctx, config);
        setIsLoading(false);
      }, 300);
    };

    updateGraph();

    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [timeRange]);

  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };

  const formatTimeRangeLabel = (range) => {
    switch (range) {
      case "monthly": return "Monthly View";
      case "yearly": return "Yearly View";
      default: return "Weekly View";
    }
  };

  return (
    <div className={styles.graphWrapper}>
      {/* Header Section */}
      <div className={styles.graphHeader}>
        <div className={styles.headerContent}>
          <div className={styles.titleSection}>
            <h2 className={styles.graphTitle}>Transaction Overview</h2>
            <p className={styles.graphSubtitle}>Track your financial activity</p>
          </div>
          <div className={styles.controlsSection}>
            <div className={styles.timeRangeSelector}>
              <label className={styles.selectorLabel}>Period:</label>
              <select
                value={timeRange}
                onChange={handleTimeRangeChange}
                className={styles.modernSelect}
                disabled={isLoading}
              >
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className={styles.chartContainer}>
        <div className={styles.chartWrapper}>
          {isLoading && (
            <div className={styles.loadingOverlay}>
              <div className={styles.loadingSpinner}></div>
              <span className={styles.loadingText}>Updating chart...</span>
            </div>
          )}
          <canvas
            ref={canvasRef}
            className={styles.chartCanvas}
          ></canvas>
        </div>

        {/* Chart Footer */}
        <div className={styles.chartFooter}>
          <div className={styles.chartInfo}>
            <span className={styles.currentPeriod}>{formatTimeRangeLabel(timeRange)}</span>
            <span className={styles.dataSource}>Real-time data</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionGraph;
