import { Inter } from "next/font/google";
import { ContextStore } from "./context/AuthContext";
import { TimerContextProvider } from "./context/TimerContext";
import { Suspense } from "react";
import Analytics from "./hooks/Analytics";
import LayoutWrapper from "./components/LayoutWrapper";
import { SSEProvider } from "./context/SSEContext";

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Suspense>
          <Analytics />
          <TimerContextProvider>
            <ContextStore>
              <SSEProvider>
                <LayoutWrapper>{children}</LayoutWrapper>
              </SSEProvider>
            </ContextStore>
          </TimerContextProvider>
        </Suspense>
      </body>
    </html>
  );
}
