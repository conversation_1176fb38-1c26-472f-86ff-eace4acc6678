"use client";
import { useTimer } from "../context/TimerContext";
import { useEffect } from "react";
import { ToastContainer } from "react-toastify";
import SessionModal from "../components/SessionRefreshModal/page";

export default function LayoutWrapper({ children }) {
  const { showModal, handleSessionContinue, handleSessionEnd, startTimer } =
    useTimer();

  return (
    <>
      {children}

      {!!showModal && (
        <SessionModal
          onContinue={handleSessionContinue}
          onCancel={handleSessionEnd}
        />
      )}
    </>
  );
}
