"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import styles from "./forgotpass.module.css";
import line from "../../../../public/line.svg";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { forgotPassApi } from "@/app/api/onboarding/forgotPassword";
import linkedIn from "../../../../public/assets/socials/linkedin.svg";
import facebook from "../../../../public/assets/socials/facebook.svg";
import socialImg from "../../../../public/social.png";
require("dotenv").config();

const login = () => {
  const router = useRouter();

  const [email, setEmail] = useState("");

  const Data = {
    email: email,
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/password-reset/`;

  const handleEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setEmail(inputValue);
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    try {
      const res = await forgotPassApi(URL, Data);

      if (res.status === 200) {
        toast.success(res.data.message);
      } else {
        toast.error("Login failed.");
      }
    } catch (error) {
      toast.error("Incorrect Email ID or Password");
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          <h1 className={styles.heading}>Forgot Password</h1>
          <div className={styles.subHeading}>
            Please enter your registered email address to reset your password.
          </div>

          <form action="" onSubmit={onSubmit}>
            <div className={styles.emailContainer}>
              <div className={styles.email}>
                <label className={styles.nameLabel} htmlFor="email">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  maxLength={260}
                  value={email}
                  onChange={handleEmail}
                  required
                />
                <div className={styles.emailLink}>
                  You will receive a link in your email to set a new password.
                </div>
              </div>
            </div>

            <div className={styles.loginBtnContainer}>
              <button type="submit" className={styles.loginBtn}>
                Next
              </button>
            </div>
          </form>
          <ToastContainer />
        </div>
      </div>
      <div className={styles.rightContainer}>
        <div className={styles.rightBody}>
          {/* Security Badge */}
          <div className={styles.securityBadge}>
            Secured Platform
          </div>
          
          {/* Animated Illustration */}
          <div className={styles.illustrationContainer}>
            <div className={styles.globalNetwork}>
              {/* Network Nodes */}
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              <div className={styles.networkNode}></div>
              
              {/* Connection Lines */}
              <div className={styles.connectionLine}></div>
              <div className={styles.connectionLine}></div>
              <div className={styles.connectionLine}></div>
              
              {/* Money Symbols */}
              <div className={styles.moneySymbol}>$</div>
              <div className={styles.moneySymbol}>€</div>
              <div className={styles.moneySymbol}>£</div>
              
              {/* Security Shield */}
              <div className={styles.securityShield}>
                <svg viewBox="0 0 24 24">
                  <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1ZM10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                </svg>
              </div>
              
              {/* Floating Currency Symbols */}
              <div className={styles.currencyFloat}>¥</div>
              <div className={styles.currencyFloat}>₹</div>
              <div className={styles.currencyFloat}>₦</div>
            </div>
          </div>
          
          <div className={styles.textContainer}>
            <div>
              <div className={styles.firstLine}>
                Secure Account Recovery
              </div>
              <div className={styles.secondLine}>
                Reset your password securely with our encrypted recovery system. We'll send you a secure link to create a new password and regain access to your Remflow account.
              </div>
              <div className={styles.lastLine}>
                Your account security is our priority. All recovery requests are monitored and protected with bank-level encryption.
              </div>
              
              {/* Feature Highlights */}
              <div className={styles.featureHighlights}>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM10 17L6 13L7.41 11.59L10 14.17L16.59 7.58L18 9L10 17Z"/>
                    </svg>
                  </div>
                  <span>Encrypted recovery process</span>
                </div>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                    </svg>
                  </div>
                  <span>Secure email verification</span>
                </div>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10M10,22C9.75,22 9.54,21.82 9.5,21.58L9.13,18.93C8.5,18.68 7.96,18.34 7.44,17.94L4.95,18.95C4.73,19.03 4.46,18.95 4.34,18.73L2.34,15.27C2.21,15.05 2.27,14.78 2.46,14.63L4.57,12.97L4.5,12L4.57,11L2.46,9.37C2.27,9.22 2.21,8.95 2.34,8.73L4.34,5.27C4.46,5.05 4.73,4.96 4.95,5.05L7.44,6.05C7.96,5.66 8.5,5.32 9.13,5.07L9.5,2.42C9.54,2.18 9.75,2 10,2H14C14.25,2 14.46,2.18 14.5,2.42L14.87,5.07C15.5,5.32 16.04,5.66 16.56,6.05L19.05,5.05C19.27,4.96 19.54,5.05 19.66,5.27L21.66,8.73C21.79,8.95 21.73,9.22 21.54,9.37L19.43,11L19.5,12L19.43,13L21.54,14.63C21.73,14.78 21.79,15.05 21.66,15.27L19.66,18.73C19.54,18.95 19.27,19.04 19.05,18.95L16.56,17.95C16.04,18.34 15.5,18.68 14.87,18.93L14.5,21.58C14.46,21.82 14.25,22 14,22H10M11.25,4L10.88,6.61C9.68,6.86 8.62,7.5 7.85,8.39L5.44,7.35L4.69,8.65L6.8,10.2C6.4,11.37 6.4,12.64 6.8,13.8L4.68,15.36L5.43,16.66L7.86,15.62C8.63,16.5 9.68,17.14 10.87,17.38L11.24,20H12.76L13.13,17.39C14.32,17.14 15.37,16.5 16.14,15.62L18.57,16.66L19.32,15.36L17.2,13.81C17.6,12.64 17.6,11.37 17.2,10.2L19.31,8.65L18.56,7.35L16.15,8.39C15.38,7.5 14.32,6.86 13.12,6.62L12.75,4H11.25Z"/>
                    </svg>
                  </div>
                  <span>Account security settings</span>
                </div>
                <div className={styles.featureItem}>
                  <div className={styles.featureIcon}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                  </div>
                  <span>24/7 support assistance</span>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className={styles.trustIndicators}>
                <div className={styles.trustItem}>
                  <div className={styles.trustNumber}>99.9%</div>
                  <div className={styles.trustLabel}>Uptime</div>
                </div>
                <div className={styles.trustItem}>
                  <div className={styles.trustNumber}>$50M+</div>
                  <div className={styles.trustLabel}>Processed</div>
                </div>
                <div className={styles.trustItem}>
                  <div className={styles.trustNumber}>50K+</div>
                  <div className={styles.trustLabel}>Users</div>
                </div>
              </div>
            </div>
            
            {/* bottom section */}
            <div className={styles.bottomSection}>
              <div className={styles.socialLinksCont}>
                <a
                  href="https://www.facebook.com/remflw"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    width={20}
                    height={20}
                    src={facebook}
                    alt="Facebook"
                  />
                </a>
                <a
                  href="https://www.linkedin.com/company/remflow/"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    width={20}
                    height={20}
                    src={linkedIn}
                    alt="LinkedIn"
                  />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default login;
