"use client";
import React, { useState } from "react";
import styles from "./historyCard.module.css";
import { getTradeById } from "@/app/api/tradeApis/historyPageTradeApi";
import { peerDecideTradeApi } from "@/app/api/tradeApis/peerDecideTrade";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = ({
  id,
  orderNo,
  dateCreated,
  status = "Notified",
  trade_amount,
  indicative_fx_rate,
  listing_id,
  payin_currency_id__currency_code,
  payin_option_id__payment_method,
  payout_currency_id__currency_code,
  payout_option_id__payment_method,
  peer_id = "23",
  max_liquidity,
  min_liquidity,
  availableLiquidity,
  terms,
  finalTradeFee,
}) => {
  const [passedRes, setPassedRes] = useState([]);

  const router = useRouter();
  const timestamp = dateCreated;
  const datePart = timestamp
    ? new Date(timestamp).toISOString().split("T")[0]
    : "";

  const handleTradeById = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade-by-id/?order_id=${id}`
      );

      setPassedRes(res.data.data);
      setTimeout(() => {
        router.push(
          `/pages/trade/${id}?orderNo=${orderNo}&name=${peer_id}&available_liquidity=${availableLiquidity}&min_liquidity=${min_liquidity}&max_liquidity=${max_liquidity}&payIn_option=${payin_option_id__payment_method}&payOut_option=${payout_option_id__payment_method}&rate=${finalTradeFee}&terms=${terms}&time=${datePart}&payIn_currency=${payin_currency_id__currency_code}&payOut_currency=${payout_currency_id__currency_code}`
        );
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handlePeerDecisionToTradeAccept = async () => {
    try {
      const res = await customFetchWithToken.post(
        `/peer-trade-request/?order_id=${id}&flag=Accepted`
      );

      toast.success(res.data.message);
      setTimeout(() => {
        router.push(
          `/pages/trade/${id}?orderNo=${orderNo}&name=${peer_id}&available_liquidity=${availableLiquidity}&min_liquidity=${min_liquidity}&max_liquidity=${max_liquidity}&payIn_option=${payin_option_id__payment_method}&payOut_option=${payout_option_id__payment_method}&rate=${indicative_fx_rate}&terms=${terms}&time=${datePart}&payIn_currency=${payin_currency_id__currency_code}&payOut_currency=${payout_currency_id__currency_code}`
        );
      }, 1500);
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };
  const handlePeerDecisionToTradeReject = async () => {
    try {
      const res = await customFetchWithToken.post(
        `/peer-trade-request/?order_id=${id}&flag=${"Rejected"}`
      );

      toast.success(res.data.message);
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };

  return (
    <div className={styles.history_container}>
      <div className={styles.wrapper}>
        <div className={styles.cardMid}>
          {/* <div className={styles.cardMidTitle}>Outcome</div> */}

          <div
            className={`${styles.cardMidTag} ${
              status.toLowerCase() === "pending"
                ? styles.not
                : status.toLowerCase() === "expired"
                ? styles.expired
                : status.toLowerCase() === "ongoing"
                ? styles.ongoing
                : status.toLowerCase() === "rejected"
                ? styles.rejected
                : status.toLowerCase() === "completed"
                ? styles.completed
                : status.toLowerCase() === "notified"
                ? styles.notified
                : status.toLowerCase() === "cancelled"
                ? styles.cancelled
                : ""
            }`}
          >
            <div>{status}</div>
          </div>
        </div>
      </div>
      <div className={styles.infoCont}>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Order Number</div>
          <div className={styles.cardBotTag}>{orderNo}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Listing Id</div>
          <div className={styles.cardBotTag}>{listing_id}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Date</div>
          <div className={styles.cardBotTag}>{datePart}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Available Liquidity</div>
          <div className={styles.cardBotTag}>{availableLiquidity}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Max Liquidity</div>
          <div className={styles.cardBotTag}>{max_liquidity}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Min Liquidity</div>
          <div className={styles.cardBotTag}>{min_liquidity}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Trade Amount</div>
          <div className={styles.cardBotTag}>{trade_amount}</div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Indicative FX Rate</div>
          <div className={styles.cardBotTag}>
            {indicative_fx_rate ? Number(indicative_fx_rate).toFixed(2) : ""}
          </div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Payin Currency</div>
          <div className={styles.cardBotTag}>
            {payin_currency_id__currency_code}
          </div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Payin Payment Method</div>
          <div className={styles.cardBotTag}>
            {payin_option_id__payment_method}
          </div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Payout Currency</div>
          <div className={styles.cardBotTag}>
            {payout_currency_id__currency_code}
          </div>
        </div>
        <div className={styles.carBottom}>
          <div className={styles.cardBotTitle}>Payout Payment Method</div>
          <div className={styles.cardBotTag}>
            {payout_option_id__payment_method}
          </div>
        </div>
        {peer_id ? (
          <div className={styles.carBottom}>
            <div className={styles.cardBotTitle}>Peer Id</div>
            <div className={styles.cardBotTag}>{peer_id}</div>
          </div>
        ) : (
          ""
        )}
        {status === "ongoing" ? (
          <div className={styles.toTrade}>
            <button onClick={handleTradeById} className={styles.tradeNavBtn}>
              Go To trade Page
            </button>
          </div>
        ) : (
          ""
        )}
      </div>
      <ToastContainer />
    </div>
    // <div className={styles.history_container}>
    //   <div className={styles.historyCards}>
    //     <div className={styles.cardTop}>
    //       <button onClick={handleTradeById} className={styles.tradeNavBtn}>
    //         Go To trade Page
    //       </button>
    //     </div>
    //     <div className={styles.cardTop}>Order Number</div>
    //     <div className={styles.cardBotTag}>{orderNo}</div>
    //     {status === "Not Confirmed" && !peer_id ? (
    //       <div className={styles.peerDecisionCont}>
    //         <div className={styles.peerDecisionBtns}>
    //           <button
    //             className={styles.accept}
    //             onClick={handlePeerDecisionToTradeAccept}
    //           >
    //             Accept
    //           </button>
    //           <button
    //             className={styles.reject}
    //             onClick={handlePeerDecisionToTradeReject}
    //           >
    //             Reject
    //           </button>
    //         </div>
    //       </div>
    //     ) : (
    //       ""
    //     )}
    //     <div className={styles.cardMid}>
    //       <div className={styles.cardMidTitle}>Outcome</div>
    //       <div
    //         className={`${styles.cardMidTag} ${
    //           status === "Not Confirmed"
    //             ? styles.not
    //             : status === "expired"
    //             ? styles.expired
    //             : status === "Ongoing"
    //             ? styles.ongoing
    //             : status === "Rejected"
    //             ? styles.rejected
    //             : status === "Completed"
    //             ? styles.completed
    //             : status === "Notified"
    //             ? styles.notified
    //             : ""
    //         }`}
    //       >
    //         <div>{status}</div>
    //       </div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Listing Id</div>
    //       <div className={styles.cardBotTag}>{listing_id}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Date</div>
    //       <div className={styles.cardBotTag}>{datePart}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Available Liquidity</div>
    //       <div className={styles.cardBotTag}>{availableLiquidity}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Max Liquidity</div>
    //       <div className={styles.cardBotTag}>{max_liquidity}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Min Liquidity</div>
    //       <div className={styles.cardBotTag}>{min_liquidity}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Trade Amount</div>
    //       <div className={styles.cardBotTag}>{trade_amount}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Indicative FX Rate</div>
    //       <div className={styles.cardBotTag}>{indicative_fx_rate}</div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Payin Currency</div>
    //       <div className={styles.cardBotTag}>
    //         {payin_currency_id__currency_code}
    //       </div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Payin Payment Method</div>
    //       <div className={styles.cardBotTag}>
    //         {payin_option_id__payment_method}
    //       </div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Payout Currency</div>
    //       <div className={styles.cardBotTag}>
    //         {payout_currency_id__currency_code}
    //       </div>
    //     </div>
    //     <div className={styles.carBottom}>
    //       <div className={styles.cardBotTitle}>Payout Payment Method</div>
    //       <div className={styles.cardBotTag}>
    //         {payout_option_id__payment_method}
    //       </div>
    //     </div>
    //     {peer_id ? (
    //       <div className={styles.carBottom}>
    //         <div className={styles.cardBotTitle}>Peer Id</div>
    //         <div className={styles.cardBotTag}>{peer_id}</div>
    //       </div>
    //     ) : (
    //       ""
    //     )}
    //   </div>
    //   <ToastContainer />
    // </div>
  );
};

export default page;
