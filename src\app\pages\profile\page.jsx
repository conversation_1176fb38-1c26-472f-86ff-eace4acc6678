"use client";
import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import styles from "./profile.module.css";
import Layout from "../../components/Layout/page";
import ProfileImg from "../../../../public/assets/profile/profileImg.png";
import tick from "../../../../public/assets/tick.png";
import {
  userVerificationPeerApi,
  getPeerDetailsApi,
} from "@/app/api/UserRegistration/userRegistartion";

import Login from "@/app/sign/login/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  updatePassApi,
  profileInfoChangeApi,
  deleteProfileApi,
  silverStatusProfileApi,
  getUserProfileInfo,
} from "../../api/profileApiFolder/profileApis";
import upload from "../../../../public/assets/upload.png";
import { useRouter } from "next/navigation";
import QRCodeSVG from "qrcode.react";
import { generateQRToken, verifyQRToken } from "@/app/api/2fa/generateToken";
import { Tooltip } from "react-tooltip";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import ConfirmationModal from "../../components/profileConfirmationModal/page";

const page = () => {
  const authTokenRef = useRef(null);
  const userIdRef = useRef(null);
  const [uploadMark, setUploadMark] = useState(false);
  const [uploadMark1, setUploadMark1] = useState(false);
  const [uploadMark2, setUploadMark2] = useState(false);
  const [proofOfAdderss, setProofOfAdderss] = useState("");
  const [sourceOfFunds, setSourceOfFunds] = useState("");
  const [bankStatement, setBankStatement] = useState("");
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [mobile, setMobile] = useState("");
  const [oldPass, setOldPass] = useState("");
  const [newPass, setNewPass] = useState("");
  const [confirmPass, setConfirmPass] = useState("");
  const [profilePic, setProfilePic] = useState("");
  const [preview, setPreview] = useState("");
  const [phoneCountryArray, setPhoneCountryArray] = useState([]);
  const [countryPhone, setCountryPhone] = useState("");
  const [secret, setSecret] = useState("");
  const [contact, setContact] = useState("");
  const [telegramId, setTelegramId] = useState("");
  const [whatsappId, setWhatsappId] = useState("");
  const [weChatId, setWeChatId] = useState("");
  const [verifyOtp, setVerifyOtp] = useState("");
  const [otherMesesgingId, setOtherMesesgingId] = useState("");
  const [newFile, setNewFile] = useState(null);
  const [qrBtn, setQrBtn] = useState("2FA authentication setup");
  const issuer = "remflow";
  const [showDeleteProfileModal, setShowDeleteProfileModal ] = useState(false)
console.log("showDeleteProfileModal", showDeleteProfileModal)
  let userEmail;

  if (typeof window !== "undefined") {
    userEmail = localStorage.getItem("userEmail");
  }

  const phnCountryRef = useRef();

  const generateQRTokenFn = async (e) => {
    e.preventDefault();
    try {
      const res = await customFetchWithToken.post("/generate-2fa-token/");
      setSecret(res.data.base32);
      // setQrBtn("Regenerate QR Code");
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };
  const cancel2fa = () => {
    setSecret("");
  };
  const otpAuthUrl = `otpauth://totp/${issuer}:${encodeURIComponent(
    userEmail
  )}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;

  const bodyTokenOtp = {
    otp: verifyOtp,
  };
  const verifyQRTokenFn = async (e) => {
    e.preventDefault();
    try {
      const res = await customFetchWithToken.post(
        "/submit-2fa-token/",
        bodyTokenOtp
      );

      if (res.status === 200) {
        toast.success("2FA authentication successfull");
      }
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };

  const router = useRouter();

  let verificationStatus;
  let userIdNumber;
  let token;
  if (typeof window !== "undefined") {
    userIdNumber = localStorage.getItem("userID");
    token = sessionStorage.getItem("user");
    verificationStatus = localStorage.getItem("verificationStatus");

    if (token) {
      authTokenRef.current = token;
    }
    if (userIdNumber) {
      userIdRef.current = userIdNumber;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const handleImageChange = (event) => {
    const file = event.target.files[0];

    // Array of allowed file extensions
    const allowedExtensions = ["image/jpeg", "image/png"];

    // Check if the file has one of the allowed extensions
    if (allowedExtensions.includes(file.type)) {
      // Create a blob URL for the image preview
      const previewUrl = URL.createObjectURL(file);

      // Update the preview state
      setPreview(previewUrl);

      // Set the profile picture state
      setProfilePic(file);
    } else {
      alert("Invalid file type. Only JPEG and PNG files are allowed.");
    }
  };

  const Data1 = {
    telegram_id: telegramId,
    whatsapp_id: `+${countryPhone}${whatsappId}`,
    wechat_id: weChatId,
    any_other_id: otherMesesgingId,
  };
  const handleVerification = async () => {
    // if (secret && !verifyOtp) {
    //   toast.error("VerifyOTP first");
    //   return;
    // }
    toast.warn("Sending");

    try {
      const res = await customFetchWithToken.post("/peer-registration/", Data1);
      toast.success(res.data.message);
      // setTimeout(function () {
      //   router.push("/pages/searchads");
      // }, 1500);
    } catch (error) {
      toast.error(error.response.data.message);
      console.error(error);
      // setTimeout(function () {
      //   router.push("/pages/searchads");
      // }, 1500);
    }
  };

  const fetchPeerDetailsApi = async () => {
    try {
      const res = await customFetchWithToken.get("/get-peer-details/");
      setTelegramId(res.data.data?.telegram_id);
      setWhatsappId(res.data.data?.whatsapp_id);
      setWeChatId(res.data.data?.wechat_id);
      setOtherMesesgingId(res.data.data?.any_other_id);
    } catch (error) {
      console.error(error);
    }
  };

  // const fetchAndCreateFile1 = async (profilePicUrl) => {
  //   try {
  //     const response = await fetch(profilePicUrl);
  //     const blob = await response.blob();
  //     console.log("Original Blob type:", blob.type); // Debugging: check original blob type
  //     const fileType =
  //       blob.type === "binary/octet-stream" ? "image/jpeg" : blob.type;
  //     const file = new File([blob], "profile_picture", { type: fileType });
  //     console.log("Created File with overridden type:", file); // Debugging: check created File
  //     return file;
  //   } catch (error) {
  //     console.error("Error fetching or creating file:", error);
  //     return null;
  //   }
  // };

  const handleFirstNameChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setFirstName("");
    } else {
      setFirstName(inputValue);
    }
  };
  const handleFirstLastChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setLastName("");
    } else {
      setLastName(inputValue);
    }
  };

  const handleOldPasswordChange = () => {
    const value = e.target.value;
    const inputValue = value.replace(
      /[^a-zA-Z0-9!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|-]/g,
      ""
    );
    if (inputValue.length > 20) {
      setOldPass("");
    } else {
      setOldPass(inputValue);
    }
  };

  const handleNewPasswordChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(
      /[^a-zA-Z0-9!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|-]/g,
      ""
    );
    if (inputValue.length > 20) {
      setNewPass("");
    } else {
      setNewPass(inputValue);
    }
  };

  const handleConfirmPasswordChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(
      /[^a-zA-Z0-9!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|-]/g,
      ""
    );
    if (inputValue.length > 20) {
      setConfirmPass("");
    } else {
      setConfirmPass(inputValue);
    }
  };

  const handleTelegramId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setTelegramId("");
    } else {
      setTelegramId(inputValue);
    }
  };

  const handleWhatsappId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setWhatsappId("");
    } else {
      setWhatsappId(inputValue);
    }
  };

  const handleWeChatId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setWeChatId("");
    } else {
      setWeChatId(inputValue);
    }
  };
  const handleOtherMesesgingId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setOtherMesesgingId("");
    } else {
      setOtherMesesgingId(inputValue);
    }
  };

  const fetchAndCreateFile = async (profilePicUrl) => {
    try {
      const response = await fetch(profilePicUrl);
      const blob = await response.blob();

      const fileType =
        blob.type === "binary/octet-stream" ? "image/jpeg" : blob.type;
      const file = new File([blob], "profile_picture", { type: fileType });

      return file;
    } catch (error) {
      console.error("Error fetching or creating file:", error);
      return null;
    }
  };
  const profileInfoChangeFunc = async () => {
    try {
      let newFile1;
      if (typeof profilePic == "string") {
        newFile1 = await fetchAndCreateFile(profilePic);
      } else {
        newFile1 = profilePic;
      }
      if (newFile1) {
        setNewFile(newFile1);

        // Create a new FormData object
        const formData = new FormData();
        formData.append("first_name", firstName);
        formData.append("last_name", lastName);
        formData.append("phoneNumber", mobile);
        formData.append("profile_picture", newFile1);
        // formData.append("profile_picture", newFile1);

        // for (let [key, value] of formData.entries()) {
        //   console.log(`Blob${key}:`, value);
        // }

        const res = await customFetchWithToken.put(
          "/edit-user-details/",
          formData
        );

        toast.success(res.data.message);
      } else {
        console.error("Failed to fetch or create file");
        toast.error("Failed to fetch or create file");
      }
    } catch (error) {
      console.error(error);

      // toast.error(error.response.data.message);
    }
  };

  const Data = {
    old_password: oldPass,
    new_password: newPass,
    confirm_password: confirmPass,
  };

  const upadtePassFunc = async () => {
    try {
      const res = await customFetchWithToken.post(
        "/update-user-password/",
        Data
      );

      toast.success(res.data.message);
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };

  const deleteProfileFunc = async () => {
    setShowDeleteProfileModal(true);
  };

  
  const handleDeleteConfirm = async () => {
    const Data = {
      flag: "delete",
    };
    try {
      const res = await customFetchWithToken.delete(
        "/edit-user-details/",
        Data
      );

      toast.success(res.data.message);
      setTimeout(() => {
        localStorage.clear();
        router.push("/sign/login");
      }, 1500);
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
    setShowDeleteProfileModal(false);
  };

  // silver status

  const handleMobileNum = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");
    if (inputValue.length > 20) {
      setMobile("");
    } else {
      setMobile(inputValue);
    }
  };

  // getProfileInfoAPI

  const getProfileInfoAPIFunc = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");
      setFirstName(res.data.data.firstname);
      setLastName(res.data.data.lastname);
      setEmail(res.data.data.email);
      setMobile(Number(res.data.data.mobile));
      setProfilePic(res.data.data.img_logo);
    } catch (error) {
      console.error(error);
    }
  };

  // getProfileInfoAPI

  const handleProofOfAddress = (e) => {
    // Array of allowed file extensions
    const allowedExtensions = ["image/jpeg", "image/png", "application/pdf"];

    // Check if the file has one of the allowed extensions
    if (allowedExtensions.includes(e.target.files[0].type)) {
      setProofOfAdderss(e.target.files[0]);
      setUploadMark(true);
    } else {
      // Optionally, show an error message or alert to inform the user about the invalid file type
      alert("Invalid file type. Only JPEG, PNG, and PDF files are allowed.");
    }
  };

  const handleSourceOfFunds = (e) => {
    const allowedExtensions = ["image/jpeg", "image/png", "application/pdf"];

    if (allowedExtensions.includes(e.target.files[0].type)) {
      setSourceOfFunds(e.target.files[0]);
      setUploadMark1(true);
    } else {
      alert("Invalid file type. Only JPEG, PNG, and PDF files are allowed.");
    }
  };

  const handleBankStatement = (e) => {
    const allowedExtensions = ["image/jpeg", "image/png", "application/pdf"];

    if (allowedExtensions.includes(e.target.files[0].type)) {
      setBankStatement(e.target.files[0]);
      setUploadMark2(true);
    } else {
      alert("Invalid file type. Only JPEG, PNG, and PDF files are allowed.");
    }
  };

  // silverHandleSubmitFunction

  const handleUserStatusDocsSubmit = async (e) => {
    e.preventDefault();
    const silverStatusFormData = new FormData();
    silverStatusFormData.append("user_id", userIdRef.current);
    silverStatusFormData.append("flag", "document");
    silverStatusFormData.append("bank_statement", bankStatement);
    silverStatusFormData.append("source_of_fund", sourceOfFunds);
    silverStatusFormData.append("proof_of_address", proofOfAdderss);
    if (!proofOfAdderss) {
      // Display an alert or handle the validation error as needed
      toast.error("Upload Prooof of Address");
      return;
    }
    if (!sourceOfFunds) {
      // Display an alert or handle the validation error as needed
      toast.error("Upload Source of Funds");
      return;
    }
    if (!bankStatement) {
      // Display an alert or handle the validation error as needed
      toast.error("Upload Bank Statement");
      return;
    }

    toast.warn("Sending");
    try {
      const res = await customFetchWithToken.post(
        "/upload-document/",
        silverStatusFormData
      );

      toast.success(res.data.message);
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };
  // silverHandleSubmitFunction

  // silver status

  //mobileCountry

  const getPhoneCountryDropDown = async () => {
    try {
      if (phnCountryRef.current == false) return;
      const res = await customFetchWithToken.get("/country-code/");
      setPhoneCountryArray(res.data.data);
      phnCountryRef.current = false;
    } catch (error) {
      console.error(error);
    }
  };

  //mobileCountry

  useEffect(() => {
    getProfileInfoAPIFunc();
  }, []);
  useEffect(() => {
    fetchPeerDetailsApi();
  }, []);
  useEffect(() => {
    getPhoneCountryDropDown();
  }, []);

  return (
    <>
      {/* {authTokenRef.current ? ( */}
      <div>
        <Layout title="Profile">
          {verificationStatus === "User_Detail" ? (
            <div
              style={{
                fontSize: "12px",
                textAlign: "left",
                color: "gray",
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                width: "100%",
                marginTop: "30px",
              }}
            >
              <span style={{ marginRight: "5px" }}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  x="0px"
                  y="0px"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                >
                  <path d="M 12 0 C 5.371094 0 0 5.371094 0 12 C 0 18.628906 5.371094 24 12 24 C 18.628906 24 24 18.628906 24 12 C 24 5.371094 18.628906 0 12 0 Z M 12 2 C 17.523438 2 22 6.476563 22 12 C 22 17.523438 17.523438 22 12 22 C 6.476563 22 2 17.523438 2 12 C 2 6.476563 6.476563 2 12 2 Z M 12 5.8125 C 11.816406 5.8125 11.664063 5.808594 11.5 5.84375 C 11.335938 5.878906 11.183594 5.96875 11.0625 6.0625 C 10.941406 6.15625 10.851563 6.285156 10.78125 6.4375 C 10.710938 6.589844 10.6875 6.769531 10.6875 7 C 10.6875 7.226563 10.710938 7.40625 10.78125 7.5625 C 10.851563 7.71875 10.941406 7.84375 11.0625 7.9375 C 11.183594 8.03125 11.335938 8.085938 11.5 8.125 C 11.664063 8.164063 11.816406 8.1875 12 8.1875 C 12.179688 8.1875 12.371094 8.164063 12.53125 8.125 C 12.691406 8.085938 12.816406 8.03125 12.9375 7.9375 C 13.058594 7.84375 13.148438 7.71875 13.21875 7.5625 C 13.289063 7.410156 13.34375 7.226563 13.34375 7 C 13.34375 6.769531 13.289063 6.589844 13.21875 6.4375 C 13.148438 6.285156 13.058594 6.15625 12.9375 6.0625 C 12.816406 5.96875 12.691406 5.878906 12.53125 5.84375 C 12.371094 5.808594 12.179688 5.8125 12 5.8125 Z M 10.78125 9.15625 L 10.78125 18.125 L 13.21875 18.125 L 13.21875 9.15625 Z"></path>
                </svg>
              </span>
              Cannot change First Name and Last Name after submitting KYC
              documents!
            </div>
          ) : (
            ""
          )}
        { 
            showDeleteProfileModal && (  
              <ConfirmationModal 
                isOpen={showDeleteProfileModal}
                onClose={() => setShowDeleteProfileModal(false)}
                onConfirm={handleDeleteConfirm}
                title="Confirm Deletion"
                message="Are you sure you want to delete your profile? This action cannot be undone."
                confirmButtonText="Delete Account"
                cancelButtonText="Cancel"
              />  )}
           
          <div className={styles.mainContainer}>
            <div className={styles.topContainer}>
              <div className={styles.leftarea}>
                <div className={styles.profiePic}>
                  <Image
                    unoptimized={true}
                    src={profilePic}
                    alt="Profile Picture"
                    width={104}
                    height={104}
                  />
                  {/* <img src={profilePic} alt="" /> */}
                </div>
                <div className={styles.changeProfile}>
                  <label
                    htmlFor="profilePictureInput"
                    className={styles.fileInputLabel}
                  >
                    Change Picture
                  </label>
                  <input
                    className={styles.fileInput}
                    type="file"
                    id="profilePictureInput"
                    onChange={handleImageChange}
                    required
                  />
                </div>

                {/* <div className={styles.changeProfile}>Change Picture</div> */}
              </div>

              <div className={styles.midarea}>
                <div className={styles.firstName}>
                  <label htmlFor="firstname">First Name</label>
                  <input
                    id="firstname"
                    type="text"
                    className={styles.firstNameInput}
                    placeholder="John"
                    maxLength={260}
                    value={firstName}
                    readOnly={verificationStatus === "User_Detail"}
                    onChange={handleFirstNameChange}
                  />
                </div>
                <div className={styles.firstName}>
                  <label htmlFor="email">Email</label>
                  <input
                    id="email"
                    type="email"
                    maxLength={260}
                    className={styles.firstNameInput}
                    placeholder="<EMAIL>"
                    value={email}
                    readOnly
                  />
                </div>
                <div className={styles.buttonWarpperWeb}>
                  <div
                    className={styles.saveChangeBtn}
                    onClick={profileInfoChangeFunc}
                  >
                    Save Changes
                  </div>
                  <div
                    className={styles.deleteAccBtn}
                    onClick={deleteProfileFunc}
                  >
                    Delete Account
                  </div>
                </div>
              </div>
              <div className={styles.rightarea}>
                <div className={styles.firstName}>
                  <label htmlFor="lastname">Last Name</label>
                  <input
                    id="lastname"
                    type="text"
                    className={styles.firstNameInput}
                    placeholder="John"
                    value={lastName}
                    maxLength={260}
                    readOnly={verificationStatus === "User_Detail"}
                    onChange={handleFirstLastChange}
                  />
                </div>
                <div className={styles.firstName}>
                  <label htmlFor="number">Mobile Number</label>
                  <input
                    id="number"
                    type="text"
                    className={styles.firstNameInput}
                    maxLength={20}
                    placeholder="**********"
                    value={mobile == 0 ? "" : mobile}
                    onChange={handleMobileNum}
                  />
                </div>

                <div className={styles.buttonWarpperMob}>
                  <div
                    className={styles.saveChangeBtn}
                    onClick={profileInfoChangeFunc}
                  >
                    Save Changes
                  </div>
                  <div
                    className={styles.deleteAccBtn}
                    onClick={deleteProfileFunc}
                  >
                    Delete Account
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.bottomConatiner}>
              <div className={styles.botTitle}>Change Password</div>
              <div className={styles.botBody}>
                <div className={styles.passChange}>
                  <div className={styles.oldpassCont}>
                    <label htmlFor="oldpass">Old Password</label>
                    <input
                      id="oldpass"
                      type="text"
                      maxLength={260}
                      className={styles.oldPass}
                      placeholder="enter old password"
                      onChange={handleOldPasswordChange}
                    />
                  </div>{" "}
                  <div className={styles.oldpassCont}>
                    <label htmlFor="newPassword">
                      New Password{" "}
                      <svg
                        data-tooltip-id="my-tooltip1"
                        data-tooltip-content={`Password must be 8 to 25 characters long and must contain at least one uppercase letter, one lowercase letter, one numeric digit, and one special character.`}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 512 512"
                        width={10}
                        height={10}
                      >
                        <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z" />
                      </svg>
                    </label>
                    <input
                      id="newPassword"
                      type="text"
                      maxLength={260}
                      className={styles.oldPass}
                      placeholder="enter new password"
                      onChange={handleNewPasswordChange}
                    />
                  </div>
                  <div className={styles.oldpassCont}>
                    <label htmlFor="confirmPassword">
                      Confirm Password{" "}
                      <svg
                        data-tooltip-id="my-tooltip"
                        data-tooltip-content="Confirm Passowrd Should match New Password"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 512 512"
                        width={10}
                        height={10}
                      >
                        <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z" />
                      </svg>
                    </label>
                    <input
                      id="confirmPassword"
                      type="text"
                      maxLength={260}
                      className={styles.oldPass}
                      placeholder="confirm new password"
                      onChange={handleConfirmPasswordChange}
                    />
                  </div>
                </div>
              </div>
              <div className={styles.botBtn}>
                <button onClick={upadtePassFunc}>Update Password</button>
              </div>
            </div>
            {/* authentication @fa */}
            <div className={styles.bottomConatiner}>
              <div className={styles.botTitle1}>
                Setup Two Factor Authentication
              </div>
              <div className={styles.botBody}>
                <div className={styles.passChange}>
                  <div className={styles.submitBtnCont}>
                    {!secret ? (
                      <button
                        className={styles.submitBtn}
                        onClick={generateQRTokenFn}
                      >
                        {qrBtn}
                      </button>
                    ) : (
                      ""
                    )}
                    <p>
                      {secret ? (
                        <div style={{ fontSize: "14px" }}>
                          {" "}
                          Scan the code with your authenticator app to setup 2FA
                        </div>
                      ) : (
                        ""
                      )}
                    </p>
                    <div></div>

                    {secret.length > 1 ? (
                      <div className={styles.qrContainet}>
                        <QRCodeSVG value={otpAuthUrl} size={100} />
                        <div className={styles.twoFactorContainer}>
                          <input
                            type="text"
                            className={styles.verifyInput}
                            maxLength={260}
                            placeholder="Enter Genearted Code"
                            onChange={(e) => setVerifyOtp(e.target.value)}
                          />
                          <div>
                            <button
                              className={styles.cancelBtn}
                              onClick={cancel2fa}
                            >
                              Cancel
                            </button>
                            <button
                              className={styles.verifyBtn}
                              onClick={verifyQRTokenFn}
                            >
                              Verify code
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* authentication @fa */}

            {/* THISSSSS */}

            <div className={styles.peerFormConatiner}>
              <div className={styles.botTitle1}>Peer Registation</div>
              <div className={styles.formContainer}>
                <form action="">
                  {/* names */}
                  <div className={styles.formWrapper}>
                    <div className={styles.firstNameC}>
                      <div className={styles.firstNameLabel}>Telegram ID</div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="text"
                          id="firstname"
                          value={telegramId}
                          maxLength={260}
                          onChange={handleTelegramId}
                        />
                      </div>
                    </div>
                    <div className={styles.firstNameC}>
                      <div className={styles.firstNameLabel}>Whatsapp ID </div>
                      <div className={styles.firstNameInput}>
                        {whatsappId && whatsappId.length < 1 ? (
                          <span className={styles.courtyOptions}>
                            <select
                              style={{ width: "150px", height: "40px" }}
                              name="country"
                              value={countryPhone}
                              onChange={(e) => setCountryPhone(e.target.value)}
                              id="country"
                              required
                            >
                              <option value="-1">Select Country</option>
                              {phoneCountryArray.map((el) => (
                                <option value={el.phone_code}>
                                  {el.country_name}
                                </option>
                              ))}
                            </select>
                          </span>
                        ) : (
                          ""
                        )}
                        <input
                          type="text"
                          value={whatsappId}
                          id="lastname"
                          maxLength={260}
                          onChange={handleWhatsappId}
                        />
                      </div>
                    </div>
                  </div>
                  {/* names */}
                  {/* names */}
                  <div className={styles.formWrapper}>
                    <div className={styles.firstNameC}>
                      <div className={styles.firstNameLabel}>WeChat ID</div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="text"
                          id="firstname"
                          maxLength={260}
                          value={weChatId}
                          onChange={handleWeChatId}
                        />
                      </div>
                    </div>
                    <div className={styles.firstNameC}>
                      <div className={styles.firstNameLabel}>
                        Any other messaging app ID
                      </div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="text"
                          maxLength={260}
                          value={otherMesesgingId}
                          id="lastname"
                          onChange={handleOtherMesesgingId}
                        />
                      </div>
                    </div>
                  </div>
                  {/* names */}

                  <div className={styles.submitBtnCont}></div>
                </form>
              </div>
              <div className={styles.botBtn1} onClick={handleVerification}>
                <button>Submit</button>
              </div>
            </div>
            {verificationStatus === "Dash_Board" ? (
              <div className={styles.lastContainer}>
                <div className={styles.silverWrapper}>
                  <div className={styles.silverTag}>
                    <div className={styles.silverTagName}>
                      To Achieve Silver Status
                    </div>
                  </div>
                  <div className={styles.formContainer}>
                    <form action="">
                      {/* names */}
                      {/* proof of address */}

                      <div className={styles.formWrapper}>
                        <div className={styles.addressName}>
                          <div className={styles.firstNameLabel}>
                            Upload proof of address* ( please upload a Proof of
                            address dated within 3 months, bank statement,
                            utility bill or government issued letter)
                          </div>

                          <div className={`${styles.addressNameInput} ${proofOfAdderss ? styles.fileUploaded : ""}`}>
                            <label
                              htmlFor="POF"
                              style={{
                                fontSize: "12px",
                                width: "100%",
                                paddingLeft: "10px",
                                color: proofOfAdderss ? '#76d376' : "grey",
                                cursor: "pointer",
                              }}
                            >
                              {proofOfAdderss
                                ? "Proof of Address Uploaded waiting for review"
                                : "Click here to Upload"}
                            </label>
                            <input
                              className={styles.fileInput}
                              type="file"
                              id="POF"
                              onChange={handleProofOfAddress}
                              required
                            />

                            <Image
                              className={styles.upload}
                              src={uploadMark ? tick : upload}
                              width={15}
                              height={15}
                              alt="backPNG"
                            />
                          </div>
                        </div>
                      </div>

                      {/*  proof of address */}
                      {/*SOF */}
                      <div className={styles.formWrapper}>
                        <div className={styles.addressName}>
                          <div className={styles.firstNameLabel}>
                            Source of Funds* ( please upload a Proof of source
                            of funds showing how the money you will transact on
                            remflow was earned. E.g. employer salary slip,
                            contract of sale, invoice, trading account
                            screenshot, plus a bank statement showing the funds
                            received to your account from this source)
                          </div>

                          <div className={`${styles.addressNameInput} ${sourceOfFunds ? styles.fileUploaded : ""}`}>
                            <label
                              htmlFor="sof"
                              style={{
                                fontSize: "12px",
                                width: "100%",
                                paddingLeft: "10px",
                                color: sourceOfFunds ? '#76d376' : "grey",
                                cursor: "pointer",
                              }}
                            >
                              {sourceOfFunds
                                ? "Source of Funds Uploaded waiting for review"
                                : "Click here to Upload"}
                            </label>
                            <input
                              className={styles.fileInput}
                              type="file"
                              id="sof"
                              onChange={handleSourceOfFunds}
                              required
                            />

                            <Image
                              className={styles.upload}
                              src={uploadMark1 ? tick : upload}
                              width={15}
                              height={15}
                              alt="backPNG"
                            />
                          </div>
                        </div>
                      </div>

                      {/* SOF*/}
                      {/* Bank statements*/}

                      <div className={styles.formWrapper}>
                        <div className={styles.addressName}>
                          <div className={styles.firstNameLabel}>
                            Upload Bank Statement* (please upload a recent bank
                            statement showing your name, address, account number
                            and a transaction, account balance can be hidden if
                            you wish)
                          </div>

                          <div className={`${styles.addressNameInput} ${bankStatement ? styles.fileUploaded : ""}`}>
                            <label
                              htmlFor="bank_statement"
                              style={{
                                fontSize: "12px",
                                width: "100%",
                                paddingLeft: "10px",
                                color: bankStatement ? '#76d376' : "grey",
                                cursor: "pointer",
                              }}
                            >
                              {bankStatement
                                ? "Bank Statement Uploaded waiting for review"
                                : "Click here to Upload"}
                            </label>
                            <input
                              className={styles.fileInput}
                              type="file"
                              id="bank_statement"
                              onChange={handleBankStatement}
                              required
                            />

                            <Image
                              className={styles.upload}
                              src={uploadMark2 ? tick : upload}
                              width={15}
                              height={15}
                              alt="backPNG"
                            />
                          </div>
                        </div>
                      </div>

                      {/*  Bank statement */}
                      <div className={styles.nextBtnCont}>
                        <button
                          className={styles.nextsubmitBtn}
                          onClick={handleUserStatusDocsSubmit}
                        >
                          Submit
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
                <div className={styles.goldWrapper}>
                  <div className={styles.goldTag}>
                    <div className={styles.goldNameTag}>To Achieve Gold</div>
                  </div>
                  <li className={styles.goldPara}>
                    100 Successful Trades with Remflow
                  </li>
                  <li className={styles.goldPara}>
                    {" "}
                    Escrow Funds with Remflow
                  </li>
                  <li className={styles.goldPara}>
                    {" "}
                    Maintain 90%+ rating on REMFLOW transactions
                  </li>
                </div>
              </div>
            ) : (
              ""
            )}
          </div>
          <Tooltip
            id="my-tooltip"
            style={{
              color: "#fff",
              fontSize: "12px",
            }}
          />
          <Tooltip
            id="my-tooltip1"
            style={{
              color: "#fff",
              fontSize: "12px",
            }}
          />
          <Tooltip
            id="my-tooltip2"
            style={{
              color: "#fff",
              fontSize: "12px",
            }}
          />
        </Layout>
        <ToastContainer />
      </div>
      {/* ) : (
        <Login />
      )} */}
    </>
  );
};

export default page;
