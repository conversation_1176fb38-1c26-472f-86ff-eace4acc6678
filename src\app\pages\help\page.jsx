"use client";
import { useState } from "react";
import styles from "./help.module.css";
import Layout from "../../components/Layout/page";

const Page = () => {
  const [activeAccordion, setActiveAccordion] = useState(null);

  const toggleAccordion = (index) => {
    setActiveAccordion(activeAccordion === index ? null : index);
  };

  const faqData = [
    {
      question: "How do I create a trade request?",
      answer: "To create a trade request, navigate to the Search page, find a suitable listing, enter your desired amount, and click 'Send Trade Request'. You'll need to have a recipient account set up before initiating the trade."
    },
    {
      question: "What payment methods are supported?",
      answer: "Remflow supports various payment methods including bank transfers, digital wallets, and cryptocurrency. You can add and manage your payment methods in the 'Saved Payment Methods' section under Accounts."
    },
    {
      question: "How do I add a recipient account?",
      answer: "Go to Accounts > Add Accounts > Add Recipient Accounts. Fill in the required details including recipient name, email, country, and payout method. Make sure all information is accurate to avoid delays."
    },
    {
      question: "What should I do if my trade is disputed?",
      answer: "If you encounter issues with a trade, you can file a dispute by going to the Dispute section. Provide detailed information, upload evidence, and our support team will review your case within 24-48 hours."
    },
    {
      question: "How do I deposit funds to my Remflow wallet?",
      answer: "Navigate to Remflow Funds, select your preferred cryptocurrency network (ETH or TRX), and use the provided wallet address to deposit USDT. Funds typically reflect within 10-30 minutes depending on network congestion."
    },
    {
      question: "What are the trading fees?",
      answer: "Trading fees vary by listing and are set by individual traders. You can see the exact fee before confirming any trade. Fees typically range from 0.5% to 5% depending on the currency pair and payment method."
    },
    {
      question: "How do I enable two-factor authentication (2FA)?",
      answer: "Go to your Profile settings, scroll to the Security section, and click 'Generate QR Code'. Scan the QR code with your authenticator app and enter the verification code to enable 2FA for enhanced account security."
    },
    {
      question: "What happens if I don't complete a trade on time?",
      answer: "Each trade has a specific time duration set by the trader. If you don't complete the trade within this timeframe, it may be automatically cancelled. Always check the timer and complete your part of the trade promptly."
    },
    {
      question: "How do I contact customer support?",
      answer: "You can contact our support team through the Help Desk section. Create a support ticket with your query, attach any relevant evidence, and our team will respond within 24 hours. For urgent issues, include 'URGENT' in your ticket title."
    },
    {
      question: "Can I cancel a trade request?",
      answer: "Yes, you can cancel a trade request before it's accepted by the other party. Once a trade is accepted and in progress, cancellation requires mutual agreement or dispute resolution through our support team."
    }
  ];

  const helpTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Help Center</h1>
      <p className={styles.pageSubtitle}>
        Find answers to commonly asked questions
      </p>
    </div>
  );

  return (
    <>
      <div style={{ overflowX: "hidden", width: "100%" }}>
        <Layout title={helpTitle}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.mobileHeader}>
            <div className={styles.headerContent}>
              <h1 className={styles.pageTitle}>Help Center</h1>
              <p className={styles.pageSubtitle}>
                Find answers to commonly asked questions
              </p>
            </div>
          </div>

          <div className={styles.rightContainerBody}>
            <div className={styles.helpContainer}>
              <div className={styles.welcomeSection}>
                <div className={styles.welcomeIcon}>❓</div>
                <h2 className={styles.welcomeTitle}>How can we help you?</h2>
                <p className={styles.welcomeDescription}>
                  Browse through our frequently asked questions to find quick answers to common queries about Remflow's services.
                </p>
              </div>

              <div className={styles.faqSection}>
                <h3 className={styles.faqTitle}>
                  <span className={styles.faqIcon}>📋</span>
                  Frequently Asked Questions
                </h3>

                <div className={styles.accordionContainer}>
                  {faqData.map((faq, index) => (
                    <div key={index} className={styles.accordionItem}>
                      <button
                        className={`${styles.accordionHeader} ${
                          activeAccordion === index ? styles.accordionHeaderActive : ''
                        }`}
                        onClick={() => toggleAccordion(index)}
                        aria-expanded={activeAccordion === index}
                        aria-controls={`accordion-content-${index}`}
                      >
                        <span className={styles.accordionQuestion}>{faq.question}</span>
                        <span className={`${styles.accordionIcon} ${
                          activeAccordion === index ? styles.accordionIconRotated : ''
                        }`}>
                          ▼
                        </span>
                      </button>

                      <div
                        id={`accordion-content-${index}`}
                        className={`${styles.accordionContent} ${
                          activeAccordion === index ? styles.accordionContentActive : ''
                        }`}
                        role="region"
                        aria-labelledby={`accordion-header-${index}`}
                      >
                        <div className={styles.accordionAnswer}>
                          {faq.answer}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className={styles.contactSection}>
                <div className={styles.contactCard}>
                  <div className={styles.contactIcon}>💬</div>
                  <h4 className={styles.contactTitle}>Still need help?</h4>
                  <p className={styles.contactDescription}>
                    Can't find what you're looking for? Our support team is here to help.
                  </p>
                  <button className={styles.contactButton}>
                    <span className={styles.buttonIcon}>🎫</span>
                    Create Support Ticket
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Layout>
      </div>
    </>
  );
};

export default Page;
