"use client";
import { createContext, useRef, useEffect, useContext, useState } from "react";
import useWebSocket, { ReadyState } from "react-use-websocket";
import { usePathname } from "next/navigation";

export const AppContext = createContext();

const SHARED_SECRET = "your-32-byte-keyhersssssssssse!!"; // Must be 32 characters

export const ContextStore = ({ children }) => {
  const pathname = usePathname();
  const connection = useRef(null);
  const [shouldReconnect, setShouldReconnect] = useState(true);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [recentMessage, setRecentMessage] = useState("");
  const [messageHistory, setMessageHistory] = useState([]);

  const encryptMessage = async (message) => {
    try {
      const iv = crypto.getRandomValues(new Uint8Array(16));
      const key = await crypto.subtle.importKey(
        "raw",
        new TextEncoder().encode(SHARED_SECRET),
        { name: "AES-CBC" },
        false,
        ["encrypt"]
      );
      const encodedMessage = new TextEncoder().encode(message);
      const encrypted = await crypto.subtle.encrypt(
        { name: "AES-CBC", iv },
        key,
        encodedMessage
      );
      const combined = new Uint8Array([...iv, ...new Uint8Array(encrypted)]);
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error("Encryption error:", error);
      throw error;
    }
  };

  const decryptMessage = async (encryptedMessageBase64) => {
    try {
      // Input validation
      if (typeof encryptedMessageBase64 !== "string") {
        console.error(
          "decryptMessage received non-string input:",
          encryptedMessageBase64
        );
        return "Invalid encrypted message format";
      }

      // console.log("Raw encrypted message:", encryptedMessageBase64);

      // Sanitize the Base64 string
      let sanitized = encryptedMessageBase64
        .replace(/-/g, "+")
        .replace(/_/g, "/")
        .replace(/\s/g, "")
        .replace(/[^A-Za-z0-9+/=]/g, ""); // Remove invalid characters

      // Add padding if necessary
      while (sanitized.length % 4) {
        sanitized += "=";
      }

      // console.log("Sanitized Base64 (with padding):", sanitized);

      // Try to decode a small portion first to validate
      try {
        const testDecode = atob(sanitized.substring(0, 4));
        // console.log("Base64 validation successful");
      } catch (e) {
        console.error("Base64 validation failed:", e);
        console.log(
          "First 10 characters of sanitized string:",
          sanitized.substring(0, 10)
        );
        return "Invalid Base64 encoding";
      }

      // Proceed with full decoding
      const binaryString = atob(sanitized);
      // console.log("Binary string length:", binaryString.length);

      // Convert to Uint8Array
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Validate IV length
      if (bytes.length < 16) {
        console.error("Input too short to contain IV");
        return "Encrypted message too short";
      }

      const iv = bytes.slice(0, 16);
      const encryptedData = bytes.slice(16);

      // console.log("IV length:", iv.length);
      // console.log("Encrypted data length:", encryptedData.length);

      // Import the key
      const key = await crypto.subtle.importKey(
        "raw",
        new TextEncoder().encode(SHARED_SECRET),
        { name: "AES-CBC" },
        false,
        ["decrypt"]
      );

      // Decrypt the data
      const decrypted = await crypto.subtle.decrypt(
        { name: "AES-CBC", iv },
        key,
        encryptedData
      );

      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error("Decryption error:", error);
      console.error("Error name:", error.name);
      console.error("Error message:", error.message);
      if (error instanceof DOMException) {
        console.error("DOM Exception code:", error.code);
      }
      return "Decryption failed";
    }
  };
  const token =
    typeof window !== "undefined" ? sessionStorage.getItem("user") : null;
  const shouldConnectWebSocket =
    pathname !== "/sign/login" && pathname !== "/" && token;

  const {
    sendMessage: originalSendMessage,
    lastMessage,
    readyState,
    lastJsonMessage,
  } = useWebSocket(
    shouldConnectWebSocket
      ? `wss://dev.remflow.net/ws/transaction/?token=${token}`
      : null,
    {
      onOpen: () => {
        console.log("Connected!!");
        setShouldReconnect(true);
      },
      onClose: () => {
        console.log("Disconnected!");
        setShouldReconnect(false);
      },
      onError: (error) => {
        console.error("WebSocket error:", error);
        setShouldReconnect(false);
      },
      shouldReconnect: () => shouldReconnect,
      onMessage: async (message) => {
        try {
          let messageData = message.data;

          if (messageData instanceof Blob) {
            messageData = await messageData.text();
          }

          // console.log("Processed message:", messageData);
          const decryptedMessage = await decryptMessage(messageData);
          // console.log("decryptMessage123", decryptedMessage);
          setRecentMessage(JSON.parse(decryptedMessage));
        } catch (error) {
          console.error("Error processing message:", error);
        }
      },
    }
  );

  const sendMessage = async (message) => {
    try {
      console.log("Original message:", message);
      const encryptedMessage = await encryptMessage(message);
      // console.log("Encrypted message:", encryptedMessage);

      if (readyState === ReadyState.OPEN) {
        originalSendMessage(encryptedMessage);
      } else {
        console.error("WebSocket not open");
      }
    } catch (error) {
      console.error("Encryption failed:", error);
    }
  };

  useEffect(() => {
    if (lastJsonMessage) {
      setMessageHistory((prev) => [...prev, lastJsonMessage]);
    }
  }, [lastJsonMessage]);
  // console.log("lastJsonMessageAA", lastJsonMessage);

  useEffect(() => {
    if (readyState === ReadyState.CLOSED && token) {
      console.log("Attempting to reconnect...");
      const timeout = Math.min(1000 * 2 ** reconnectAttempts, 30000);

      const timeoutId = setTimeout(() => {
        console.log("Reconnecting now...");
        setShouldReconnect(true);
        setReconnectAttempts((prev) => prev + 1);
      }, timeout);

      return () => clearTimeout(timeoutId);
    }
  }, []);

  useEffect(() => {
    if (readyState === ReadyState.OPEN) {
      setReconnectAttempts(0);
    }
  }, [readyState]);

  connection.current = {
    [ReadyState.CONNECTING]: "Connecting",
    [ReadyState.OPEN]: "Open",
    [ReadyState.CLOSING]: "Closing",
    [ReadyState.CLOSED]: "Closed",
    [ReadyState.UNINSTANTIATED]: "Uninstantiated",
  }[readyState];

  return (
    <AppContext.Provider
      value={{
        connection,
        sendMessage,
        messageHistory,
        recentMessage,
        lastJsonMessage,
        readyState,
        ReadyState,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useWebsocketContext = () => useContext(AppContext);
