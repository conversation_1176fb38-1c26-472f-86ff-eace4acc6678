"use client";
import React from "react";
import styles from "./closedCases.module.css";

const page = ({ handleModalFunc }) => {
  // console.log("UpiIdArr", UpiId);
  return (
    <div className={styles.account_container}>
      <div className={styles.accountCards}>
        <div className={styles.cardTop}>Order number</div>
        <button className={styles.cardPayNameTag} onClick={handleModalFunc}>
          12345
        </button>

        <div className={styles.cardMid}>
          <div className={styles.cardBotTag}>Resaon</div>
          <div className={styles.cardMidTitle}>Funds not received</div>
          <div className={styles.cardMidTag}>
            <div>Status: Success</div>
          </div>
          <div className={styles.cardMidTag}>
            <div>Date: 25-12-2023</div>
          </div>
        </div>
        {/* <div className={styles.cardBottom}>
          <div className={styles.cardBotTitle}>Notes from admin</div>
          <div className={styles.cardBotBody}>
            stry. Lorem Ipsum has been the industry's standard dummy text ever
            since the 1500s, when an unknown printer took a galley of type and
            scrambled it to make a type specimen book. It has survived not only
            five centuries, but also the leap into electronic typesetting,
            remaining essentially unchanged. It
          </div>
         
        </div> */}
        {/* <div className={styles.UploadBtn}>Upload Evidence</div>
        <div className={styles.SupportMsgBtn}>Message Support</div> */}
        <div className={styles.submitBtn}>Closed</div>
      </div>
      <div className={styles.addNewPaymethodContainer}></div>
    </div>
  );
};

export default page;
