<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7c9fe3bf-7bb9-496f-a797-7903cf169f65" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/AccountsCard/accountCard.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/AccountsCard/accountCard.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/AccountsCard/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/AccountsCard/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/AccountsCardUser/accountCard.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/AccountsCardUser/accountCard.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/AccountsCardUser/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/AccountsCardUser/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/Dashboard/TransactionTable/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/Dashboard/TransactionTable/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/Dashboard/TransactionTable/transactionTable.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/Dashboard/TransactionTable/transactionTable.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/DisputeCard/disputeCard.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/DisputeCard/disputeCard.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/DisputeCard/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/DisputeCard/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/DisputeDropdown/disputeDrop.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/DisputeDropdown/disputeDrop.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/DisputeDropdown/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/DisputeDropdown/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/HistoryCard/historyCard.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/HistoryCard/historyCard.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/HistoryCard/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/HistoryCard/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/LoadingSpinner/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/LoadingSpinner/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/LoadingSpinner/spinner.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/LoadingSpinner/spinner.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/ModifyWithdrawModal/modify.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/ModifyWithdrawModal/modify.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/ModifyWithdrawModal/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/ModifyWithdrawModal/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/MyListingsCard/myListingsCard.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/MyListingsCard/myListingsCard.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/MyListingsCard/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/MyListingsCard/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/NotificationBox/notificationBox.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/NotificationBox/notificationBox.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/NotificationBox/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/NotificationBox/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/NotificationList/notificationList.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/NotificationList/notificationList.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/NotificationList/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/NotificationList/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/RequestWithdrawRemfunds/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/RequestWithdrawRemfunds/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/RequestWithdrawRemfunds/requestWithdrawRemfunds.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/RequestWithdrawRemfunds/requestWithdrawRemfunds.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/SearchResultsComp/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/SearchResultsComp/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/SidebarHeader/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/SidebarHeader/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/SidebarHeader/sidebarHeader.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/SidebarHeader/sidebarHeader.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/paymentMethod/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/paymentMethod/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/paymentMethod/pay.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/paymentMethod/pay.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/sidebar/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/sidebar/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/sidebar/sidebar.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/sidebar/sidebar.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/withdrawHistory/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/withdrawHistory/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/components/withdrawHistory/withdrawHistory.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/components/withdrawHistory/withdrawHistory.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/accounts/accounts.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/accounts/accounts.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/accounts/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/accounts/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/addlisting/addlisting.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/addlisting/addlisting.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/addlisting/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/addlisting/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/dashboard/dashboard.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/dashboard/dashboard.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/dashboard/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/dashboard/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/dispute/dispute.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/dispute/dispute.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/dispute/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/dispute/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/history/history.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/history/history.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/history/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/history/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/mylistings/mylistings.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/mylistings/mylistings.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/mylistings/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/mylistings/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/remflowFunds/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/remflowFunds/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/remflowFunds/remFunds.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/remflowFunds/remFunds.module.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/searchads/page.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/searchads/page.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/pages/searchads/search.module.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/pages/searchads/search.module.css" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2yHGZz3ltxEBEAIPsRpJTort5oL" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev__sse&quot;,
    &quot;js.debugger.nextJs.config.created.client&quot;: &quot;true&quot;,
    &quot;js.debugger.nextJs.config.created.server&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\WebStorm 2025.1.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="npm.Next.js: server-side">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-WS-251.26094.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7c9fe3bf-7bb9-496f-a797-7903cf169f65" name="Changes" comment="" />
      <created>1749484145749</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749484145749</updated>
      <workItem from="1749484147247" duration="1792000" />
      <workItem from="1749535683513" duration="21864000" />
      <workItem from="1749737455794" duration="2298000" />
      <workItem from="1750110746907" duration="4000" />
      <workItem from="1750236416834" duration="30000" />
      <workItem from="1751010133146" duration="2000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>