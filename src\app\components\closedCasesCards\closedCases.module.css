.account_container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 20px;

}

.accountCards {
    background-color: #ffffff;
    width: 250px;
    height: auto;
    flex-wrap: wrap;
    color: #5C5C5C;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    border-radius: 5px;
    filter: drop-shadow(5px 2px 4px #ececec);
    padding: 15px;
    margin: 5px 5px;
}

.cardTop {
    text-decoration: underline;

}

.cardMidTitle {
    color: #eb0f3f;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    /* margin-left: 10px; */
}

.cardMidBody {
    color: #353132;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    /* margin-left: 10px; */
}

/* 
.cardMidTag {
    color: #50CD89;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
    background-color: #93e2b7;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    margin-left: 10px;
} */

.cardBotTitle {
    color: #c23232;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    /* margin-left: 10px; */
}

.cardBotTag {
    color: rgb(0, 0, 0);
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    /* margin-left: 10px; */
}

.cardPayNameTag {
    width: 100px;
    height: 30px;
    color: rgb(255, 255, 255);
    background-color: #4153ED;
    font-family: Poppins;
    font-size: 12px;
    font-style: bold;
    font-weight: 800;
    line-height: normal;
    border-radius: 5px;
    margin-bottom: 10px;
    margin-top: 5px;
    border: none;
    outline: none;
    cursor: pointer;
}

.cardBottom {
    margin-top: 40px;
}

.modalHeaderCont {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.submitBtn {
    align-self: center;
    display: flex;
    width: 150px;
    height: 20px;
    padding: 10px 50px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #FFF;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    border-radius: 5px;
    background: #202022;
    margin: 10px 0px;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        width: 234px;
    }
}

.SupportMsgBtn {
    display: flex;
    width: 150px;
    height: 20px;
    padding: 10px 50px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #FFF;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    border-radius: 5px;
    background: #4153ED;
    margin-top: 15px;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        width: 234px;
    }
}

.UploadBtn {
    display: flex;
    width: 150px;
    height: 20px;
    padding: 10px 50px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    border-radius: 5px;
    background: #ffffff;
    margin-top: 15px;
    border: 1px solid black;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        width: 234px;
    }
}