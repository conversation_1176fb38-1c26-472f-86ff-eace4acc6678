.modalTop {
    width: 100%;
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #858585;
}

.left {
    width: 85%;
    height: 63px;
}


.right {
    width: 15%;
    /* background-color: #50CD89; */
    display: flex;
    justify-content: flex-end;
    /* align-items: center; */
}

.modalProfileWrapper {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    align-items: center;


}

.orders {

    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    margin-left: 10px;
    margin-right: 10px;

}


.modalName {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 11px;
    /* 78.571% */
}

.modalBottom {
    width: 100%;
    display: flex;

}

.leftB {
    width: 50%;
    display: flex;
    margin-right: auto;
    flex-direction: column;

}

.tHeader {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 11px;

    text-decoration-line: underline;
}

.termsPara {
    color: #363636;
    font-family: Poppins;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;

}

.info {
    border: 1px solid #EBEBEB;
    background: #FFF;
    display: inline-flex;
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.infoPoints {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;

}

.highlight {

    font-weight: 500;
}

.terms {
    margin-top: 30px;
}

.tHeader {
    margin-bottom: 20px;
}

.rightB {
    width: 40%;
}

.payInput {
    display: flex;
    height: 40px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    background: #F9F9F9;

}

.paySelect {
    width: 70px;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    height: 100%;
    background-color: transparent;
    border: 1px solid #BABABA;
}

.payInput input {
    outline: none;
    border: none;
    height: 100%;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    background-color: transparent;
}

.receive {
    outline: none;
    border: none;
    /* height: 100%; */
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    background-color: transparent;
}

.buttonBBUY {
    /* margin-top: 20px; */
    display: flex;
    /* width: 93%; */
    height: 40px;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #E8FFF3;
    color: #50CD89;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    cursor: pointer;

}


/* modal */

/* here */

.modalCont {
    display: flex;
    width: 700px;
    padding-bottom: 0px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 30px;
    height: auto;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.recipientInfoCont {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.recipientInfo {
    width: 150px;
    background-color: #4153ed;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
}

.NoAccTitle {
    @media screen and (max-width : 576px) {
        text-align: center;
    }
}

.recipientInfoWallet {
    background-color: #4153ed;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
}

.recipientInfo1 {
    width: 150px;
    background-color: #edcd41;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
}

.recipientInfoWallet1 {
    background-color: #edcd41;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
}



/* here */

.modalWrapper {
    display: flex;
    width: 100%;
    justify-content: center;
    font-family: 'Poppins';
    flex-direction: column;
}

.leftCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.recipientCardCont {
    margin: 10px 36px;
    display: flex;

    @media screen and (max-width : 576px) {
        flex-direction: column;

    }
}

.rightCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Poppins';

}

.timeLabelSelect {
    font-weight: 600;
    height: 40px;
    border-radius: 5px;
    font-family: 'Poppins';
}

.timeLabelWrapper {
    font-family: 'Poppins';
    display: flex;
    flex-direction: column;
}

.buttonCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Poppins';
}

.closeBtn {
    margin: 0px 10px;
    padding: 10px 30px;
    border-radius: 5px;
    background-color: grey;
    color: white;
    font-weight: 400;
    border-color: grey;
    font-family: 'Poppins';
    border: none;
    cursor: pointer;
}

.tradeBtn {
    margin: 0px 10px;
    padding: 10px 30px;
    border-radius: 5px;
    background-color: black;
    color: white;
    font-weight: 400;
    border-color: black;
    font-family: 'Poppins';
    border: none;
    cursor: pointer;
}

.NoAccountWrapper {
    height: 20vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: 'Poppins';
}

.NoAccButton {}

.NoAccButton button {
    margin: 10px 0px;
    padding: 10px;
    border-radius: 5px;
    border: none;
    background-color: #4153ed;
    color: white;
    cursor: pointer;

}

/* CSS */
.radio[type="radio"]:checked {
    color: red;
    background-color: #363636;
    /* Change the color to whatever you need */
}