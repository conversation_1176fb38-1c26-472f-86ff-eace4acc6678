"use client";
import { useEffect, useState } from "react";
import styles from "./search.module.css";
import Modal from "react-modal";
import Layout from "../../components/Layout/page";
import MobView from "../../components/searchResultMobileView/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Tooltip } from "react-tooltip";
import ReactPaginate from "react-paginate";
import SearchResultsTable from "../../components/SearchResultsComp/SearchResultsTable";
import LoadingSpinner from "../../components/LoadingSpinner/page";

const page = () => {
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
  // old Page

  const [currencyFrom, setCurrencyFrom] = useState("USDT");
  const [currencyTo, setCurrencyTo] = useState("INR");
  const [availableLiquidity, setAvailableLiquidity] = useState("");
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const [messageAccepted, setMessageAccepted] = useState("");
  const [messagePayout, setMessagePayout] = useState("");
  const [dataAccepted, setDataAccepted] = useState([]);
  const [dataPayout, setDataPayout] = useState("");
  const [payinOption, setPayinOption] = useState("");
  const [payoutOption, setPayoutOption] = useState("");
  const [advanceSearch, setAdvanceSearch] = useState(false);
  const [sortByPrice, setSortByPrice] = useState("");
  const [page, setPage] = useState(1);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hoverMobResults, setHoverMobResults] = useState(false);
  const [toUsdt, setToUsdt] = useState(true);
  const [fromUsdt, setFromUsdt] = useState(false);
  const [otherData, setOtherData] = useState([]);
  const [otherData1, setOtherData1] = useState([]);
  const [bestRate, setBestRate] = useState("");
  const [showSearchMob, setShowSearchMob] = useState(false);

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  const handleCurrencyFrom = (event) => {
    setCurrencyFrom(event.target.value);
  };

  const handleCurrencyTo = (event) => {
    setCurrencyTo(event.target.value);
  };
  const handlePayMethodFrom = (event) => {
    setPayinOption(event.target.value);
  };
  const handlePayMethodTo = (event) => {
    setPayoutOption(event.target.value);
  };
  const handleFilterTabs = (event) => {
    // Reset all input fields to default values
    setCurrencyFrom("USDT");
    setCurrencyTo("INR");
    setAvailableLiquidity("");
    setPayinOption("");
    setPayoutOption("");
    setSortByPrice("");
    setBestRate("");
    resetFetchSearchResults();
  };

  const handleEnterAmount = (e) => {
    const value = e.target.value;
    if (value < 1 || value.length > 20) {
      setAvailableLiquidity("");
    } else {
      setAvailableLiquidity(e.target.value);
    }
  };
  const handleFilterByAsc = () => {
    setSortByPrice("asc");
    fetchSearchResults();
  };

  const handleFilterByDsc = () => {
    setSortByPrice("dsc");
    fetchSearchResults();
  };
  const handleFilterByRateData = (e) => {
    const value = e.target.value;
    if (value == 1) {
      setSortByPrice("dsc");
    } else if (value == 2) {
      setSortByPrice("asc");
    }
  };

  // get req Currency 👇
  const fetchCurrencyDataFrom = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/currency-list/?currency_from=true`
      );
      const data = await resCurrency.json();
      setLoadCurrencyFrom(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchCurrencyDataTo = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/currency-list/?currency_to=true`
      );
      const data = await resCurrency.json();
      setLoadCurrencyTo(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };
  // get req Currency 👆

  //v2
  const handleCurrencyToUsdt = () => {
    setToUsdt(true);
    setFromUsdt(false);
    setData(otherData1);
  };

  const handleUsdtToCurrency = async () => {
    setToUsdt(false);
    setFromUsdt(true);
    // setData(otherData);
    setLoading(true);
    try {
      const response = await fetch(
        `${BaseURL}/get-listings-data/?currency_accepted=USDT&currency_payout=${currencyTo}&payout_option=${payoutOption}&available_liquidity=${availableLiquidity}&rate_sort=${sortByPrice}&page=${page}&flag=crypto_to_fiat`,
        {
          method: "GET",
          Authorization: `Bearer ${token}`,
        }
      );
      const resData = await response.json();

      const PassData = resData.results.data;
      const otherData1 = resData.other_listing;
      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }
      setData(PassData);
      setOtherData1(PassData);
      setOtherData(otherData1);
      setNoOfRecords(resData.count);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  //v2

  const resetFetchSearchResults = async () => {
    setToUsdt(true);
    setFromUsdt(false);
    // setData(otherData1);
    setShowSearchMob(false);
    setLoading(true);
    // console.log("bestRateF", currencyFrom);
    // console.log("bestRateT", currencyTo);
    try {
      const response = await fetch(
        `${BaseURL}/get-listings-data/?currency_accepted=USDT&currency_payout=INR&payin_option=${payinOption}&payout_option=${payoutOption}&available_liquidity=${availableLiquidity}&rate_sort=${sortByPrice}&page=${page}`,
        {
          method: "GET",
          Authorization: `Bearer ${token}`,
        }
      );
      const resData = await response.json();

      setBestRate(resData.results.best_rate?.toFixed(2));
      const PassData = resData.results.data;
      const otherData1 = resData.other_listing;
      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }
      setData(PassData);
      setOtherData1(PassData);
      setOtherData(otherData1);
      setNoOfRecords(resData.count);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSearchResults = async () => {
    setToUsdt(true);
    setFromUsdt(false);
    // setData(otherData1);
    setShowSearchMob(false);
    setLoading(true);
    // console.log("bestRateF", currencyFrom);
    // console.log("bestRateT", currencyTo);
    try {
      const response = await fetch(
        `${BaseURL}/get-listings-data/?currency_accepted=${currencyFrom}&currency_payout=${currencyTo}&payin_option=${payinOption}&payout_option=${payoutOption}&available_liquidity=${availableLiquidity}&rate_sort=${sortByPrice}&page=${page}`,
        {
          method: "GET",
          Authorization: `Bearer ${token}`,
        }
      );
      const resData = await response.json();

      setBestRate(resData.results.best_rate?.toFixed(2));
      const PassData = resData.results.data;
      const otherData1 = resData.other_listing;
      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }
      setData(PassData);
      setOtherData1(PassData);
      setOtherData(otherData1);
      setNoOfRecords(resData.count);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  //filter data by rate
  const fetchFilterByRateData = async () => {
    try {
      const response = await fetch(
        `${BaseURL}/get-listings/?currency_accepted=${currencyFrom}&currency_payout=${currencyTo}&available_liquidity=${availableLiquidity}&payout_option=${payoutOption}&payin_option=${payinOption}&page_size=10&page=1&rate_sort=${sortByPrice}`,
        {
          method: "GET",
          // headers: {
          //   Authorization: `Bearer ${authToken}`,
          // },
        }
      );
      const resData = await response.json();
      const PassData = resData.results;
      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }
      setData(PassData);

      // console.log(PassData);
    } catch (error) {
      console.error(error);
    }
  };
  //filter data by rate

  //get Payment methods

  const fetchPaymentMethodsFrom = async () => {
    if (currencyFrom.length < 1) {
      return;
    }
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyFrom}`
      );
      // const resCurrency = await fetch(`${BaseURL}/payment-list/?currency=INR`);
      const data = await resCurrency.json();
      setDataAccepted(data.data);
      setMessageAccepted(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchPaymentMethodsTo = async () => {
    if (currencyTo.length < 1) {
      return;
    }
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyTo}`
      );
      const data = await resCurrency.json();
      setDataPayout(data.data);
      setMessagePayout(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  //get Payment methods

  useEffect(() => {
    fetchCurrencyDataTo();
    fetchCurrencyDataFrom();
  }, []);

  useEffect(() => {
    fetchPaymentMethodsTo();
  }, [currencyTo]);

  // useEffect(() => {
  //   if (currencyFrom.length > 0) {
  //     fetchPaymentMethodsFrom();
  //   }
  // }, [currencyFrom]);

  useEffect(() => {
    fetchPaymentMethodsFrom();
  }, [currencyFrom]);

  const [modalIsOpen, setIsOpen] = useState(true);

  const [noOfRecords, setNoOfRecords] = useState(0);
  const itemsPerPage = 10;

  const pageCount = Math.ceil(noOfRecords / itemsPerPage);

  // setPage(pageCount);

  const handlePageClick = async (event) => {
    const newPage = event.selected + 1;
    setPage(newPage);
    setLoading(true);

    try {
      const response = await fetch(
        `${BaseURL}/get-listings-data/?currency_accepted=${currencyFrom}&currency_payout=${currencyTo}&payin_option=${payinOption}&payout_option=${payoutOption}&available_liquidity=${availableLiquidity}&rate_sort=${sortByPrice}&page=${newPage}`,
        {
          method: "GET",
          Authorization: `Bearer ${token}`,
        }
      );
      const resData = await response.json();

      setBestRate(resData.results.best_rate?.toFixed(2));
      const PassData = resData.results.data;
      const otherData1 = resData.other_listing;

      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }

      setData(PassData);
      setOtherData1(PassData);
      setOtherData(otherData1);
      setNoOfRecords(resData.count);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const openModal = () => {
    setIsOpen(!modalIsOpen);
  };
  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
    // subtitle.style.color = null;
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  Modal.setAppElement("body");
  let subtitle;

  useEffect(() => {
    fetchSearchResults();
  }, [page, sortByPrice]);
  const searchAdsTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Search Ads</h1>
      <p className={styles.pageSubtitle}>
        Find and browse available trading opportunities
      </p>
    </div>
  );

  return (
    // <div>
    //   {authToken ? (
    <div>
      <Layout title={searchAdsTitle}>
        {/* Header Section - Hidden on desktop, shown only on mobile */}
        <div className={styles.searchAdsHeader}>
          <div className={styles.headerContent}>
            <h1 className={styles.pageTitle}>Search Ads</h1>
            <p className={styles.pageSubtitle}>
              Find and browse available trading opportunities
            </p>
          </div>
        </div>
        {/* new */}
        <div className={styles.postionlWrapper}>
          <div className={styles.firstformWrapper}>
            <div className={styles.firstName}>
              <div className={styles.firstNameLabel}>
                <label className={styles.mobLabel} htmlFor="currencyPayin">
                  Currency From
                </label>
              </div>
              <div className={styles.firstNameInput}>
                <select
                  name="currencyPayin"
                  id="currencyPayin"
                  onChange={handleCurrencyFrom}
                  value={currencyFrom}
                >
                  {/* <option style={{ fontSize: "14px" }} value="">
                    Please select a currency
                  </option> */}
                  {loadCurrencyFrom?.map((currency, index) => (
                    <option style={{ fontSize: "14px" }} key={index}>
                      {currency.currency_code}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className={styles.firstName}>
              <div className={styles.firstNameLabel}>
                <label className={styles.mobLabel} htmlFor="currencyPayout">
                  Currency To
                </label>
              </div>
              <div className={styles.firstNameInput}>
                <select
                  name="currencyPayout"
                  id="currencyPayout"
                  onChange={handleCurrencyTo}
                  value={currencyTo}
                >
                  {/* <option value="">Please select a currency</option> */}
                  {loadCurrencyTo?.map((currency, index) => (
                    <option style={{ fontSize: "14px" }} key={index}>
                      {currency.currency_code}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className={styles.sec_firstName}>
              <div className={styles.firstNameLabel}>
                <label className={styles.mobLabel} htmlFor="amount_to_send">
                  Amount to send
                </label>
              </div>
              <div className={styles.firstNameInput}>
                <input
                  style={{ paddingLeft: "15px" }}
                  type="number"
                  id="amount_to_send"
                  maxLength={20}
                  placeholder="enter Amount"
                  onChange={handleEnterAmount}
                  value={availableLiquidity}
                />
              </div>
            </div>
            <div className={styles.sec_firstName}>
              {/* here */}
              <div className={styles.filterCont12}>
                <div className={styles.filterCont12}>
                  <div
                    data-tooltip-id="reset-tooltip"
                    data-tooltip-content="Reset"
                    className={
                      !advanceSearch
                        ? styles.filterAdvance
                        : styles.highlightFilter
                    }
                    onClick={handleFilterTabs}
                  >
                    <svg
                      className={`${!advanceSearch ? "" : styles.highlightFilter1} size-6`}
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"
                      />
                    </svg>
                  </div>
                  <div
                    data-tooltip-id="my-tooltip"
                    data-tooltip-content="Search"
                    className={styles.filter}
                    onClick={fetchSearchResults}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                    >
                      <path
                        d="M11.0086 9.69676C11.9163 8.45791 12.3229 6.92194 12.1469 5.39615C11.971 3.87036 11.2255 2.46727 10.0596 1.4676C8.89374 0.467925 7.39345 -0.0546141 5.8589 0.00452304C4.32436 0.0636602 2.86872 0.700112 1.78321 1.78655C0.697708 2.87298 0.0623867 4.32928 0.00435596 5.86409C-0.0536748 7.39889 0.469864 8.89902 1.47023 10.0643C2.4706 11.2297 3.87402 11.9743 5.39972 12.1491C6.92542 12.324 8.46088 11.9163 9.69891 11.0075H9.69798C9.7261 11.045 9.7561 11.0807 9.78985 11.1153L13.3991 14.7251C13.5749 14.901 13.8133 14.9999 14.062 15C14.3107 15.0001 14.5492 14.9014 14.7251 14.7256C14.901 14.5498 14.9999 14.3113 15 14.0625C15.0001 13.8138 14.9014 13.5753 14.7256 13.3993L11.1164 9.78959C11.0828 9.75565 11.0468 9.72525 11.0086 9.69676ZM11.2504 6.09264C11.2504 6.76984 11.1171 7.44041 10.8579 8.06606C10.5988 8.69171 10.219 9.26019 9.74024 9.73904C9.26146 10.2179 8.69306 10.5977 8.0675 10.8569C7.44194 11.116 6.77147 11.2494 6.09436 11.2494C5.41726 11.2494 4.74679 11.116 4.12123 10.8569C3.49566 10.5977 2.92726 10.2179 2.44848 9.73904C1.9697 9.26019 1.5899 8.69171 1.33079 8.06606C1.07167 7.44041 0.938307 6.76984 0.938307 6.09264C0.938307 4.72498 1.48153 3.41333 2.44848 2.44625C3.41543 1.47916 4.72689 0.93586 6.09436 0.93586C7.46183 0.93586 8.7733 1.47916 9.74024 2.44625C10.7072 3.41333 11.2504 4.72498 11.2504 6.09264Z"
                        fill="white"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* new */}

        <div className={styles.searchBarWrapper}>
          <div className={styles.firstName}>
            <div className={styles.firstNameLabel}>
              <label className={styles.mobLabel} htmlFor="currencyPayout">
                Payment Method From
              </label>
            </div>
            <div className={styles.firstNameInput}>
              <select
                name="currencyPayout"
                id="currencyPayout"
                onChange={handlePayMethodFrom}
                value={payinOption}
              >
                <option style={{ fontSize: "14px" }} value="">
                  Please select a payment Method{" "}
                </option>

                {messageAccepted === "Data found."
                  ? dataAccepted.map((payMethod, index) => (
                      <option style={{ fontSize: "14px" }} key={index}>
                        {payMethod.payment_method}
                      </option>
                    ))
                  : ""}
              </select>
            </div>
          </div>
          <div className={styles.firstName}>
            <div className={styles.firstNameLabel}>
              <label className={styles.mobLabel} htmlFor="currencyPayout">
                Payment Method To
              </label>
            </div>
            <div className={styles.firstNameInput}>
              <select
                name="currencyPayout"
                id="currencyPayout"
                onChange={handlePayMethodTo}
                value={payoutOption}
              >
                <option value="">Please select a payment Method</option>
                {messagePayout === "Data found."
                  ? dataPayout.map((payMethod, index) => (
                      <option key={index}>{payMethod.payment_method}</option>
                    ))
                  : ""}
              </select>
            </div>
          </div>

          <div className={advanceSearch ? styles.filterTabs : styles.hideTabs}>
            <div className={styles.firstName}>
              <div className={styles.firstNameLabel}>
                <label htmlFor="currencyPayout">Filter By Rate</label>
              </div>
              <div className={styles.firstNameInput}>
                <select
                  name="currencyPayout"
                  id="currencyPayout"
                  onChange={handleFilterByRateData}
                >
                  <option value="-1">Please select a Filter</option>
                  <option value="1">Low To High</option>
                  <option value="2">High To Low</option>
                </select>
              </div>
            </div>
            {/* <div className={styles.firstName}>
              <div className={styles.firstNameLabel}>
                <label htmlFor="currencyPayout">
                  Filter Available Liquidity
                </label>
              </div>
              <div className={styles.firstNameInput}>
                <select
                  name="currencyPayout"
                  id="currencyPayout"
                  onChange={handlePayMethodTo}
                >
                  <option value="-1">Please select a currency</option>
                  {messagePayout === "Data found."
                    ? dataPayout.map((payMethod, index) => (
                        <option key={index}>{payMethod.payment_method}</option>
                      ))
                    : ""}
                </select>
              </div>
            </div> */}
          </div>

        </div>

        {/* here */}

        <div className={styles.resultsSectionCont}>
          {loading ? (
            <LoadingSpinner />
          ) : (
            <>
              {/* Modern Table Controls - Rate Filter + Pagination */}
              <div className={styles.tableControlsContainer}>
                <div className={styles.tableControls}>
                  {/* Rate Filter Dropdown */}
                  <div className={styles.rateFilterCompact}>
                    <span className={styles.filterLabel}>Sort by Rate:</span>
                    <select
                      className={styles.rateFilterSelect}
                      value={sortByPrice === "asc" ? "asc" : sortByPrice === "dsc" ? "dsc" : ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === "asc") {
                          setSortByPrice("asc");
                        } else if (value === "dsc") {
                          setSortByPrice("dsc");
                        } else {
                          setSortByPrice("");
                        }
                      }}
                    >
                      <option value="">Default</option>
                      <option value="asc">Low to High</option>
                      <option value="dsc">High to Low</option>
                    </select>
                  </div>

                  {/* Page Info and Pagination */}
                  <div className={styles.paginationSection}>
                    <div className={styles.pageInfo}>
                      <span className={styles.pageInfoText}>
                        Page {page} of {pageCount} ({noOfRecords} results)
                      </span>
                    </div>
                    <div className={`${styles.paginationCompact} ${loading ? styles.paginationLoading : ''}`}>
                      <ReactPaginate
                        previousLabel={"‹"}
                        nextLabel={"›"}
                        breakLabel={"..."}
                        breakClassName={styles.paginationBreak}
                        activeClassName={styles.paginationActive}
                        pageCount={pageCount}
                        onPageChange={handlePageClick}
                        marginPagesDisplayed={2}
                        pageRangeDisplayed={3}
                        containerClassName={styles.paginationContainer}
                        pageClassName={styles.paginationPage}
                        pageLinkClassName={styles.paginationLink}
                        previousClassName={styles.paginationPrev}
                        previousLinkClassName={styles.paginationPrevLink}
                        nextClassName={styles.paginationNext}
                        nextLinkClassName={styles.paginationNextLink}
                        breakLinkClassName={styles.paginationBreakLink}
                        renderOnZeroPageCount={null}
                        forcePage={page - 1}
                        disableInitialCallback={true}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile Rate Sort Controls */}
              <div className={styles.rateSortContainerMob}>
                <div className={styles.headFilter}>Filter by rate</div>
                <span onClick={handleFilterByAsc}>
                  <svg
                    className={styles.upRate}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 320 512"
                    width={30}
                    height={20}
                  >
                    <path d="M182.6 137.4c-12.5-12.5-32.8-12.5-45.3 0l-128 128c-9.2 9.2-11.9 22.9-6.9 34.9s16.6 19.8 29.6 19.8H288c12.9 0 24.6-7.8 29.6-19.8s2.2-25.7-6.9-34.9l-128-128z" />
                  </svg>
                </span>
                <span onClick={handleFilterByDsc}>
                  <svg
                    className={styles.downRate}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 320 512"
                    width={30}
                    height={20}
                  >
                    <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z" />
                  </svg>
                </span>
              </div>

              {/* Currency Tabs Section */}
              {bestRate ? (
                <div className={styles.currencyTabs}>
                  <div
                    className={`${
                      toUsdt
                        ? styles.currencyToUsdtActive
                        : styles.currencyToUsdt
                    }`}
                    onClick={fetchSearchResults}
                  >
                    {currencyFrom ? `${currencyFrom}` : ""} - USDT
                  </div>
                  <div
                    className={`${
                      !fromUsdt
                        ? styles.UsdtToCurrency
                        : styles.UsdtToCurrencyActive
                    }`}
                    onClick={handleUsdtToCurrency}
                  >
                    USDT - {currencyTo ? `${currencyTo}` : ""}
                  </div>
                </div>
              ) : (
                ""
              )}
              {bestRate ? (
                <div className={styles.bestRateCont}>
                  <div className={styles.displayRate}>
                    <div className={styles.bestRate}>
                      Best Rate : {bestRate}
                    </div>{" "}
                    <span className={styles.bestRateDialogue}>
                      Combine trades from the two tabs below to get the best
                      rate.
                    </span>{" "}
                  </div>
                </div>
              ) : (
                ""
              )}
              {data && data.length ? (
                <>
                  <div className={styles.mobView}>
                  {data.map((item, index) => (
  <MobView
    key={index}
    listingId={item.listing_id}
    name={item.user_name}
    min_liquidity={item.min_liquidity}
    max_liquidity={item.max_liquidity}
    available_liquidity={item.available_liquidity}
payIn_option={item.payin_option?.payment_method || item.payin_option}
payOut_option={item.payout_option?.payment_method || item.payout_option}

    rate={item.rate}
    terms={item.terms}
    time={item.created_at}
    payIn_currency={item.currency_accepted?.currency_code || item.currency_accepted}
    payOut_currency={item.currency_payout?.currency_code || item.currency_payout}
  />
))}

                  </div>
                  <div className={styles.desktopView}>
                    <SearchResultsTable
                      data={data}
                      availableLiquidity={availableLiquidity}
                    />
                  </div>
                </>
              ) : (
                <div className={styles.noData}>No data found</div>
              )}
            </>
          )}
        </div>

        {/* Mobile Pagination - Only visible on mobile */}
        <div className={styles.paginationContMob}>
          <div>
            <ReactPaginate
              previousLabel={"‹"}
              nextLabel={"›"}
              breakLabel={"..."}
              breakClassName={styles.pagination}
              activeClassName={styles.active}
              pageCount={pageCount}
              onPageChange={handlePageClick}
              marginPagesDisplayed={1}
              pageRangeDisplayed={2}
              containerClassName={styles.pagiCont}
              pageClassName={styles.pagination}
              pageLinkClassName={styles.paginationAtag}
              previousClassName={styles.pagination}
              previousLinkClassName={styles.paginationHead}
              nextClassName={styles.paginationHead}
              nextLinkClassName={styles.paginationHead}
              breakLinkClassName={styles.pagination}
              renderOnZeroPageCount={null}
              forcePage={page - 1}
            />
          </div>
        </div>
        <ToastContainer />
        <Tooltip id="my-tooltip" />
        <Tooltip id="reset-tooltip" />
      </Layout>
    </div>
    //   ) : (
    //     <Login />
    //   )}
    // </div>
  );
};

export default page;
