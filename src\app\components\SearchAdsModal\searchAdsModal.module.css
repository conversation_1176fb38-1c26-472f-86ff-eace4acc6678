/* modal */

.modalCont {
    display: flex;
    width: 700px;
    padding-bottom: 0px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 30px;
}

.modalTop {
    width: 100%;
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #858585;
}

.left {
    width: 85%;
    height: 63px;
}


.right {
    width: 15%;
    /* background-color: #50CD89; */
    display: flex;
    justify-content: flex-end;
    /* align-items: center; */
}

.modalProfileWrapper {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    align-items: center;


}

.orders {

    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    margin-left: 10px;
    margin-right: 10px;

}


.modalName {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 11px;
    /* 78.571% */
}

.modalBottom {
    width: 100%;
    display: flex;

}

.leftB {
    width: 50%;
    display: flex;
    margin-right: auto;
    flex-direction: column;

}

.tHeader {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 11px;

    text-decoration-line: underline;
}

.termsPara {
    color: #363636;
    font-family: Poppins;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;

}

.info {
    border: 1px solid #EBEBEB;
    background: #FFF;
    display: inline-flex;
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.infoPoints {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;

}

.highlight {

    font-weight: 500;
}

.terms {
    margin-top: 30px;
}

.tHeader {
    margin-bottom: 20px;
}

.rightB {
    width: 40%;
}

.payInput {
    display: flex;
    height: 40px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    background: #F9F9F9;
    font-family: poppins;
}

.paySelect {
    width: 70px;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    height: 100%;
    background-color: transparent;
    border: 1px solid #BABABA;
}

.payInput input {
    outline: none;
    border: none;
    height: 100%;
    color: #BABABA;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    background-color: transparent;
    font-family: poppins;
}

.buttonBBUY {
    margin-top: 20px;
    display: flex;
    width: 93%;
    height: 40px;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #E8FFF3;
    color: #50CD89;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 11px;
    /* 91.667% */
}


/* modal */