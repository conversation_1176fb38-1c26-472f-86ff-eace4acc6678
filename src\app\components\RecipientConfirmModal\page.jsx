"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./reciCon.module.css";
import Modal from "react-modal";
import { getRecipientAccount } from "../../api/searchListing/searchAPI";
import Link from "next/link";
import { createTradeApi } from "../../api/tradeApis/createTrade";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useWebsocketContext } from "@/app/context/AuthContext";
const page = ({
  payOut_currency,
  modalIsOpen2,
  payOut_option,
  enteredAmount,
  listingId,
  setModalIsOpen2,
  name,
  min_liquidity,
  max_liquidity,
  available_liquidity,
  payIn_option,
  rate,
  terms,
  time,
  payIn_currency,
}) => {
  const router = useRouter();
  const singleRef = useRef(null);
  const [showModal, setShowModal] = useState(modalIsOpen2);
  const [recipientAcc, setRecipientAcc] = useState([]);
  const [recipientAccType, setRecipientAccType] = useState("");
  const [selectedListingId, setSelectedListingId] = useState(null);

  const [selectedCardId, setSelectedCardId] = useState(null);
  const [loading, setLoading] = useState(false);

  const [orderId, setOrderId] = useState(null);
  const { connection, messageHistory, sendMessage, recentMessage } =
    useWebsocketContext();

  useEffect(() => {
    if (connection.current && recentMessage?.data) {
      const dataValue = recentMessage;

      if (dataValue?.error) {
        toast.error(dataValue?.error);
      }
      if (dataValue.error === "Trade request already sent") {
        setOrderId(dataValue.data?.order_id);
        if (!singleRef.current) {
          toast.error("Trade request already sent");
        }
        singleRef.current = true;
      } else if (dataValue.data?.order_id) {
        // Note the use of dataValue.data?.order_id for consistency
        setOrderId(dataValue.data?.order_id);
        toast.success(dataValue.data?.message);
      }
    }
  }, [connection.current, recentMessage?.data]); // Removed orderId from dependencies

  const handleCardSelect = (id) => {
    setSelectedCardId(id);
    setSelectedListingId(id);
  };

  const handleSendWebsocketMsg = (selectedCardId) => {
    const payload = {
      action: "send_request",
      listing_id: Number(listingId),
      trade_amount: Number(enteredAmount),
      recipient_id: Number(selectedCardId),
    };
    sendMessage(JSON.stringify(payload));
    console.log("payload", JSON.stringify(payload));
  };

  const handleCreateTrade = async () => {
    if (selectedCardId == null) {
      toast.error("Select Your Preferred Recipient Account To Trade");
      return;
    }
    try {
      handleSendWebsocketMsg(selectedCardId);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (orderId) {
      router.push(`trade/${orderId}?orderNo=${orderId}`);
    }
  }, [orderId]);

  // const handleCreateTrade = async () => {
  //   if (selectedCardId == null) {
  //     toast.error("Select Your Prefered Recipient Account To Trade");
  //     return;
  //   }
  //   try {
  //     handleSendWebsocketMsg();

  //     // setTimeout(function () {
  //     //   router.push("/pages/history");
  //     // }, 1500);
  //     setTimeout(() => {
  //       router.push(
  //         `/pages/trade/${listingId}?orderNo=${orderId}&name=${name}&available_liquidity=${available_liquidity}&min_liquidity=${min_liquidity}&max_liquidity=${max_liquidity}&payIn_option=${payIn_option}&payOut_option=${payOut_option}&rate=${rate}&terms=${terms}&time=${time}&payIn_currency=${payIn_currency}&payOut_currency=${payOut_currency}`
  //       );
  //     }, 3500);
  //   } catch (error) {
  //     console.log(error);
  //     // toast.error(error.response.data.message);
  //   }
  // };

  const handleOpenModal = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setModalIsOpen2(false);
  };

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
    },
  };
  Modal.setAppElement("body");

  const getRecipientAccountTrade = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/get-recipient-account/?payment_option=${payOut_option}`
      );

      setRecipientAcc(res.data.data.data);
      setRecipientAccType(res.data.data.type);
    } catch (error) {
      console.error(error);
    }
  };

  const checkData = () => {
    {
      recipientAcc.map((el) => {});
    }
  };
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  if (token) {
    useEffect(() => {
      setLoading(true);

      getRecipientAccountTrade();

      checkData();
      setLoading(false);
    }, []);
  }

  return (
    <div>
      <Modal
        isOpen={showModal}
        contentLabel="onRequestClose Example"
        onRequestClose={handleCloseModal}
        style={customStyles}
      >
        <div className={styles.modalCont}>
          <p>Create Trade</p>
          {!loading ? (
            recipientAcc?.length > 0 ? (
              <div className={styles.modalWrapper}>
                <div className={styles.leftCont}>
                  Select available Recipient Accounts
                  <div className={styles.recipientCardCont}>
                    {recipientAccType === "fiat"
                      ? recipientAcc.map((el, index) => (
                          <div key={index} className={styles.recipientInfoCont}>
                            <div
                              key={index}
                              onClick={() =>
                                handleCardSelect(el.recipient_id__id)
                              }
                              className={
                                selectedCardId === el.recipient_id__id
                                  ? styles.recipientInfo1
                                  : styles.recipientInfo
                              }
                            >
                              <div>{el.recipient_id__firstname}</div>
                              <div>{el.data[0].key}</div>
                              <div>{el.data[0].value}</div>
                            </div>
                          </div>
                        ))
                      : recipientAcc.map((el, index) => (
                          <div key={index} className={styles.recipientInfoCont}>
                            <div
                              key={index}
                              onClick={() =>
                                handleCardSelect(el.wallet_address)
                              }
                              className={
                                selectedCardId === el.wallet_address
                                  ? styles.recipientInfoWallet1
                                  : styles.recipientInfoWallet
                              }
                            >
                              <div>Wallet Address: {el.wallet_address}</div>
                            </div>
                          </div>
                        ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className={styles.NoAccountWrapper}>
                <div className={styles.NoAccTitle}>
                  You don't have any saved recipient accounts to complete the
                  trade
                </div>
                <Link href="/pages/accounts" className={styles.NoAccButton}>
                  <button>Create a Recipient Account</button>
                </Link>
              </div>
            )
          ) : (
            "Loading"
          )}
          {recipientAcc !== "undefined" || recipientAcc.length < 1 ? (
            <div className={styles.buttonCont}>
              <button className={styles.closeBtn} onClick={handleCloseModal}>
                Close Modal
              </button>
              <button className={styles.tradeBtn} onClick={handleCreateTrade}>
                Create Trade
              </button>
            </div>
          ) : (
            ""
          )}
        </div>
      </Modal>
      <ToastContainer />
    </div>
  );
};

export default page;
