.paymentBoxContainer {
  width: 60%;
  height: auto;
  border-radius: 15px;
  border: 1px solid #d9d9d9;
  background: #fff;
  margin: auto;

  @media screen and (max-width: 576px) {
    width: 100%;
  }

}

.paymentBox {
  background-color: #4f535a;
  width: 100%;
}

/* padding: 25px 20px; */



.paymentHeader {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0px 15px;
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.PayMethodMain {
  border-radius: 15px;
  border: 1px solid #D9D9D9;
  background: #FFF;
  padding: 15px;
  margin: 20px 0;
}

.paymentBoxWrapper {
  margin: 20px 0;
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

}

.paymentDirection {
  padding: 0px 15px;
  /* background-color: #4153ed; */
  color: #000;
  text-align: center;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payFrom {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 50%;
  border-bottom: 1px solid black;
}

.payTo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 50%;
  border-bottom: 1px solid #efefef;
}

.activePaymentDirection {
  color: blue;
  font-size: 16px;
  border-bottom: 2px solid black;
  font-weight: 500;
}


.fifthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-x: auto;
  margin-bottom: 20px;
  /* margin-top: 55px; */
}

.paymentGateways {
  width: 100px;
  height: 100px;
  background-color: #F9F9F9;
  margin: 20px 10px 10px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.BitcoinpaymentGateways {
  width: 100px;
  height: 100px;
  background-color: #F9F9F9;
  margin: 20px 10px 10px 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* modal */
.payHeaderCont {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.payAddBtn {
  width: 100%;
  height: 40px;
  color: #FFF;
  text-align: center;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  border-radius: 5px;
  border: 1px solid #4153ED;
  background: #4153ED;

}

.modalHeader {
  color: #4153ED !important;
  font-family: Poppins !important;
  font-size: 22px !important;
  font-weight: 500 !important;
  border-bottom: 1px solid gray;
  display: flex;
  justify-content: space-between;
  width: 100%;

}

.firstName {
  width: 100%;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.firstNameLabel {
  font-size: 14px;
  color: #000;
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;


  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 95%;
  padding-left: 10px;
  padding-right: 10px;
  /* width: 490px; */
  height: 40px;
  flex-shrink: 0;

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}