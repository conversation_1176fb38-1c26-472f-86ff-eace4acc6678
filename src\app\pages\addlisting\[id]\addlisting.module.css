/* @font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
} */


.main {
  background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
  height: 103vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Poppins;
  /* position: relative; */
}

/* hamMenu */
.hamMenu {

  display: none;


  @media screen and (max-width : 576px) {
    display: block;
    width: 100%;
    background-color: #4153ED;
    position: absolute;
    top: 0;
    height: 60px;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 40px;

  }



}

.leftContainerWrapper {
  width: 20%;
  position: relative;

  @media screen and (max-width : 576px) {
    /* display: none; */
    width: 80%;
    position: absolute;
    background-color: #4153ED;
    height: 100%;
    transition: 0.4s linear;

  }
}

.leftContainerWrapperHide {
  /* width: 20%;
  position: relative; */

  @media screen and (max-width : 576px) {
    /* display: none; */
    width: 80%;
    position: absolute;
    background-color: #4153ED;
    height: 100%;
    translate: -350px;
    transition: 0.4s linear;
  }
}

/* hamMenu */

.wrapper {
  display: flex;
  width: 100%;
  height: 90vh;
  padding: 30px 10px;
  margin: auto;
  justify-content: space-between;

  @media screen and (max-width : 576px) {
    /* display: none; */
    width: 100%;
    height: 100vh;
  }
}



.leftContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 10px;

  @media screen and (max-width : 576px) {
    /* display: none; */
    width: 100%;
    /* position: absolute; */
    background-color: #4153ED;
    height: 100%;
  }
}

/* need to remove later */

.logoArea {
  height: 20%;
  width: 100%;
  background-color: #4153ed;
  border-radius: 15px 15px 15px 0px;
  display: flex;
  flex-direction: column;

  @media screen and (max-width : 576px) {
    display: none;

  }

}

.logo {
  margin-top: 5px;

  @media screen and (max-width : 576px) {
    margin-top: 0px;

  }
}

.profileBar {
  margin-top: 25px;
}

.profileBarContainer {
  background-color: #4f535a;
  width: 80%;
  height: 35px;
  margin: auto;
  border-radius: 100px;
  background: rgba(255, 255, 255, 0.17);
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.profileImg {
  margin-top: 4px;
  margin-left: 15px;
  margin-right: 5px;
}

.profileName {
  color: #fff;
  font-family: Poppins;
  font-size: 11px;
  font-weight: 500;
}

.profileDropDown {
  margin-left: auto;
  margin-right: 10px;
}

.logoContainer {
  /* width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end; */
}

.logo {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profileBar {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50%;
}

.sidebar {
  width: 100%;
  height: auto;
  border-radius: 15px;
  background: #fff;


}

.pages {
  margin-top: 30px;

  width: 100%;
  height: 100%;
}

.dashboardContainer {
  width: 100%;
  height: 47px;
  /* background-color: aqua; */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
  border-bottom: 1px solid #ececec;
}

.historyContainer {
  width: 100%;
  height: 47px;
  /* background-color: aqua; */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
}

.logoutContainer {
  /* margin-bottom: auto; */
  width: 100%;
  height: 47px;
  /* background-color: aqua; */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
  /* border-bottom: 1px solid #ECECEC; */
}

.sideIcons {
  margin-left: 20px;
  margin-right: 10px;
  color: #4153ed;
}

.dashboard {
  color: #4153ed;
}

.rightContainer {
  width: 78%;
  height: auto;
  display: flex;
  height: 100vh;

  @media screen and (max-width : 576px) {
    width: 100%;
  }

}

.rightContainerWrapper {
  width: 100%;
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
}

.rightContainerHeader {
  width: 100%;
  height: 94px;
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;

  @media screen and (max-width : 576px) {
    padding: 15px;
    height: auto;
  }
}

.rightContainerBody {

  padding: 30px;
  border-radius: 20px;
  background: #fff;

  @media screen and (max-width : 576px) {
    padding: 15px;
  }
}

.body {
  height: 100%;
  background-color: #fff;
  width: 100%;
}

.firstformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  margin-top: 30px;

  @media screen and (max-width: 576px) {
    flex-direction: column;
  }
}

.secondformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  margin-top: 30px;

  @media screen and (max-width: 576px) {
    flex-direction: column;
  }
}

.thirdformWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 30px;

  @media screen and (max-width: 576px) {
    flex-direction: column;
  }
}

.firstName {
  width: 46%;
  height: 40px;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin: 40px 0px;
  }
}

.firstNameLabel {
  font-size: 12px;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
    margin-left: 17px;
  }
}

.sec_firstName {
  width: 20%;
  height: 40px;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin-bottom: 50px;
  }
}

.firstNameLabel {
  font-size: 12px;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInput {
  display: flex;

  @media screen and (max-width: 576px) {
    padding: 15px;
  }
}

.firstNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  height: 40px;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
    padding: none;
  }
}

.firstNameInput select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  height: 40px;
  padding-right: 10px;
}

.emailBtn {
  border-radius: 0px 2px 2px 0px;
  background: #f5f5f5;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
  font-size: 10px;
  font-weight: 600;
  width: 57px;
}

.addressName {
  width: 100%;
  margin-left: 23px;

  @media screen and (max-width: 576px) {
    margin-left: 0px
  }
}

.addressNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 96%;

  height: 40px;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width: 576px) {
    width: 87%;

  }
}

.addressNameInput1 {
  border: none;
  background-color: #f9f9f9;
  width: 98%;

  height: 40px;


  @media screen and (max-width: 576px) {
    width: 97%;
  }
}

.addressNameInput1 select {
  border: none;
  background-color: transparent;
  width: 100%;
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width: 576px) {
    width: 87%;

  }
}

.addressNameSelect select {
  border: none;
  background-color: #f9f9f9;
  width: 96%;
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  outline: none;

  @media screen and (max-width: 576px) {
    width: 87%;

  }
}

.fourthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 55px;
}

.paymentBoxContainer {
  width: 97%;
  height: auto;
  border-radius: 15px;
  border: 1px solid #d9d9d9;
  background: #fff;
  margin: auto;

}

.paymentBox {
  background-color: #4f535a;
  width: 100%;
  /* padding: 25px 20px; */

}

.paymentHeader {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0px 15px;
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.paymentDirection {
  padding: 0px 15px;
  /* background-color: #4153ed; */
  color: #000;
  text-align: center;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payFrom {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 50%;
  border-bottom: 1px solid black;
}

.payTo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 50%;
  border-bottom: 1px solid #efefef;
}

.fifthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-x: auto;
  margin-bottom: 20px;
  /* margin-top: 55px; */
}

.paymentGateways {
  width: 100px;
  height: 100px;
  background-color: #F9F9F9;
  margin: 20px 10px 10px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.BitcoinpaymentGateways {
  width: 100px;
  height: 100px;
  background-color: #F9F9F9;
  margin: 20px 10px 10px 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.listing_BtnCont {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  margin-top: 30px;
}

.listing_Btn {
  width: 365px;
  height: 50px;
  border-radius: 5px;
  border: 2px solid #4153ED;
  background: #FFF;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4153ED;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    margin-bottom: 30px;

  }
}

.textAreaBox {
  width: 97%;
  font-family: poppins;
  background-color: #f9f9f9;
  border-color: #ebebeb;
  padding-left: 5px;
}

.option {
  -webkit-appearance: menulist-button;
  /* WebKit-based browsers */
  -moz-appearance: menulist-button;
  /* Firefox */
  appearance: menulist-button;
  /* Standard property */
  height: 900px;
}