"use client";
import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import styles from "./layout.module.css";
import logoImg from "../../../../public/rem_logoW.png";
import ProfileImg from "../../../../public/assets/profile/profileImg.png";
import Sidebar from "../../components/sidebar/page";
import NotificationBox from "../NotificationBox/page";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useCheckStatusApihook } from "@/app/hooks/checkKycStatushook";
import { usePathname, useRouter } from "next/navigation";
import { useTimer } from "@/app/context/TimerContext";
import SessionModal from "@/app/components/SessionRefreshModal/page";

const Page = ({ children, title }) => {
  const { isTimerActive, formattedTime, startTimer, stopTimer } = useTimer();
  const pathname = usePathname();
  const router = useRouter();
  const [hide, setHide] = useState(true);
  const [hideReason, setHideReason] = useState(false);
  const [status, setStatus] = useState("");
  const [notificationDrop, setNotificationDrop] = useState(false);

  let verifyStatus;
  let token;
  if (typeof window !== "undefined") {
    verifyStatus = localStorage.getItem("verificationStatus");
    token = sessionStorage.getItem("user");
  }

  if (verifyStatus === "Document-Verifications") {
    useCheckStatusApihook();
  }

  const getVerificationStatusFunc = async () => {
    try {
      const res = await customFetchWithToken.get("/check-status/");

      setStatus(res.data.flag);
      localStorage.setItem("verificationStatus", res.data.flag);
    } catch (error) {
      console.log(error);
    }
  };
  const handleRedirect = () => {
    if (status === "Document-Verifications") {
      router.push("/verification/status");
    }
    if (status === "User_Detail") {
      router.push("/verification/personaldetails");
    }
  };

  const handleNotificationDrop = () => {
    setNotificationDrop(!notificationDrop);
  };
  const handleNotificationModalClose = () => {
    setNotificationDrop(false);
  };

  const hideHam = () => {
    setHide(!hide);
  };

  useEffect(() => {
    var VerificationStatus = localStorage.getItem("verificationStatus");
    setStatus(VerificationStatus);
  }, []);

  if (pathname === "/pages/searchads" && token) {
    useEffect(() => {
      getVerificationStatusFunc();
    }, []);
  }

  const handleRedirection = () => {
    if (verifyStatus === "User_Detail") {
      router.push("/verification/personaldetails");
    } else if (verifyStatus === "Document-Verifications") {
      router.push("/verification/status");
    } else if (!token) {
      router.push("/");
    }
  };

  // if (pathname === "/pages/searchads" && !token) {

  // } else {
  //   useEffect(() => {
  //     getVerificationStatusFunc();
  //   }, []);
  // }

  return (
    <div>
      <div className={styles.main}>
        <div className={styles.hamMenu}>
          <div className={styles.hamMenuLeft} onClick={hideHam}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="23"
              height="16"
              viewBox="0 0 23 16"
              fill="none"
            >
              <path
                d="M0 1.13766C0 0.835932 0.11986 0.546564 0.333212 0.333212C0.546564 0.11986 0.835932 0 1.13766 0H21.6155C21.9172 0 22.2066 0.11986 22.4199 0.333212C22.6333 0.546564 22.7531 0.835932 22.7531 1.13766C22.7531 1.43938 22.6333 1.72875 22.4199 1.9421C22.2066 2.15545 21.9172 2.27531 21.6155 2.27531H1.13766C0.835932 2.27531 0.546564 2.15545 0.333212 1.9421C0.11986 1.72875 0 1.43938 0 1.13766Z"
                fill="white"
              />
              <path
                d="M0 7.99996C0 7.69824 0.11986 7.40887 0.333212 7.19552C0.546564 6.98216 0.835932 6.8623 1.13766 6.8623H21.6155C21.9172 6.8623 22.2066 6.98216 22.4199 7.19552C22.6333 7.40887 22.7531 7.69824 22.7531 7.99996C22.7531 8.30169 22.6333 8.59106 22.4199 8.80441C22.2066 9.01776 21.9172 9.13762 21.6155 9.13762H1.13766C0.835932 9.13762 0.546564 9.01776 0.333212 8.80441C0.11986 8.59106 0 8.30169 0 7.99996Z"
                fill="white"
              />
              <path
                d="M1.13766 13.7246C0.835932 13.7246 0.546564 13.8445 0.333212 14.0578C0.11986 14.2712 0 14.5605 0 14.8623C0 15.164 0.11986 15.4534 0.333212 15.6667C0.546564 15.8801 0.835932 15.9999 1.13766 15.9999H21.6155C21.9172 15.9999 22.2066 15.8801 22.4199 15.6667C22.6333 15.4534 22.7531 15.164 22.7531 14.8623C22.7531 14.5605 22.6333 14.2712 22.4199 14.0578C22.2066 13.8445 21.9172 13.7246 21.6155 13.7246H1.13766Z"
                fill="white"
              />
            </svg>
          </div>
          <div className={styles.hamMenuCenter}>
            <Image
              src={logoImg}
              alt="Picture of the logo"
              width={50}
              height={27}
            />
          </div>
          <div className={styles.hamMenuRight}>
            <Image
              src={ProfileImg}
              alt="Picture of the logo"
              width={27}
              height={27}
            />
          </div>
        </div>
        <div className={styles.wrapper}>
          <div
            className={`${
              hide
                ? styles.leftContainerWrapperHide
                : styles.leftContainerWrapper
            }`}
          >
            {/* <div className={styles.leftContainerWrapper}> */}
            <div className={styles.leftContainer}>
              <Sidebar
                token={token}
                status={status}
                handleRedirect={handleRedirect}
              />
            </div>
          </div>
          <div className={styles.rightContainer}>
            <div className={styles.rightContainerWrapper}>
              <div className={styles.rightContainerBox}>
                <div className={styles.rightContainerHeader}>
                  {typeof title === 'string' ? title : (
                    <div className={styles.desktopTitleWrapper}>
                      {title}
                    </div>
                  )}
                </div>
                <div className={styles.notificationWrapper}>
                  <NotificationBox
                    handleNotificationDrop={handleNotificationDrop}
                    notificationDrop={notificationDrop}
                  />
                </div>
              </div>
              {/* Mobile Notification Section */}
              <div className={styles.mobileNotificationSection}>
                <NotificationBox
                  handleNotificationDrop={handleNotificationDrop}
                  notificationDrop={notificationDrop}
                />
              </div>
              <div
                className={styles.rightContainerBody}
                onClick={handleNotificationModalClose}
              >
                <div className={styles.body}>{children}</div>
              </div>
            </div>
          </div>
          {/* <div style={{ position: "absolute", bottom: "10px", right: "10px" }}>
            <SessionModal />
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Page;
