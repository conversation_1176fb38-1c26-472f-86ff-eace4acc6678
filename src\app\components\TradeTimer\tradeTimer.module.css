.alertPopup {
    background-color: gainsboro;
    text-align: center;
    border-radius: 5px;
    padding: 5px 10px;
    font-size: 16px;
    font-family: 'Poppins';
    font-weight: 400;
}

.btnContainer {
    width: 100%;
}

.yesBtn {
    background-color: green;
    padding: 5px 20px;
    border-radius: 3px;
    border: none;
    color: white;
    margin: 0px 3px;
    cursor: pointer;
}

.noBtn {
    background-color: rgb(209, 23, 23);
    padding: 5px 20px;
    border-radius: 3px;
    border: none;
    color: white;
    margin: 0px 3px;
    cursor: pointer;
}

.countDownTimer {
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}