.cont {
    background-color: #070a22;
    opacity: 89%;
    padding: 20px;
    position: fixed;
    bottom: 15px;
    right: 10px;
}

.heading {
    color: aliceblue;
    font-size: 16px;
    font-family: poppins;

}

.boxBody {
    color: aliceblue;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.btnContainer {
    width: 100%;
    display: flex;
    justify-content: space-around;
}

.btnGreen {
    width: 150px;
    padding: 8px 4px;
    border-radius: 5px;
    outline: none;
    border: none;
    background-color: #34e95b;
    color: #ffffff;
    font-weight: 600;
    cursor: pointer;
}

.btnRed {
    width: 150px;
    padding: 8px 4px;
    border-radius: 5px;
    outline: none;
    border: none;
    background-color: #e93434;
    color: #ffffff;
    font-weight: 600;
    cursor: pointer;
}