.history_container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
    border-top: 1px dashed;
    padding: 20px 0px;
}

.wrapper {
    width: 100%;
}

.infoCont {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.historyCards {
    padding: 25px 35px;
    background-color: #ffffff;
    width: 100%;
    height: auto;
    flex-wrap: wrap;
    color: #5C5C5C;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    border-radius: 5px;
    filter: drop-shadow(5px 2px 4px #ececec);

    margin: 5px 5px;
}

.cardTop {
    margin-left: 10px;
}

.toTrade {
    width: 100%;
    margin-left: 5px;
    border: 1px solid #5C5C5C;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cardMidTitle {
    color: #5C5C5C;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    margin-left: 10px;
}

.cardMidTag {
    width: 100%;
    color: #fff;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
    background-color: #2d704b;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    margin-left: 10px;
}

.cardBotTitle {
    color: #5C5C5C;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    margin-left: 10px;
}

.cardBotTag {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    margin-left: 10px;
    text-align: end;
}

.cardBotTag1 {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;

}

.carBottom {
    margin: 5px 0px;
    display: flex;
    justify-content: space-between;
}

.not {
    background-color: orange;
}


.expired {
    background-color: #1b1a1a;
}

.rejected {
    background-color: orangered;
}

.ongoing {
    background-color: #4153ed;
}

.completed {
    background-color: #4aa874;
}

.notified {
    background-color: #2d704b;
}

.cancelled {
    background-color: #a39f9f;
}

.peerDecisionCont {

    margin: 5px 0px;
    color: #fff;
}

.peerDecisionBtns {
    display: flex;
}

.accept {
    width: 90px;
    padding: 10px 20px;
    margin: 0px 4px;
    background-color: green;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: poppins;
    font-weight: 500;

}

.reject {
    width: 90px;
    padding: 10px 20px;
    margin: 0px 4px;
    background-color: #ef233c;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: poppins;
    font-weight: 500;
}

.tradeNavBtn {
    width: 100%;
    padding: 5px;
    /* background-color: transparent; */
    border-radius: 5px;
    color: #1b1a1a;
    font-weight: 400;
    font-family: poppins;
    font-size: 10px;
    border: none;
    cursor: pointer;
}

.allWrapper {
    display: flex;
}

.oneToThreeWrapper {
    display: flex;
    padding: 0px 25px;
}

.oneWrapper {}

.twoWrapper {
    padding: 0px 20px;
    width: 250px;
}

.threeWrapper {
    padding: 0px 25px;
    width: 250px;
}

.fourWrapper {
    width: 170px;
}

.cardMid {
    display: flex;
    width: 100%;
}

.tradeReqDialogue {
    color: #1b1a1a;
    font-size: 14px;
    font-family: poppins;
    font-weight: 500;
    margin-top: 25px;

}